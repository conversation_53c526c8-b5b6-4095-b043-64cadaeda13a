
"use client"

import Image from 'next/image';
import React, { useState } from 'react';
import GoogleDriveEmbed from '@/components/ui/google-drive-embed';
import { Ta<PERSON>, <PERSON><PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { ChevronUp, ChevronDown } from 'lucide-react';
import AnimatedHero from '@/components/animated-hero';
import EnhancedMediaSlider from '@/components/enhanced-media-slider';
import { useSearchParams } from 'next/navigation';
import { useTranslation, getLanguageFromParams } from '@/lib/translations';





const getImageUrl = (strapiImage) => {
  if (!strapiImage) return '/logo.jpeg';

  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

  if (strapiImage.mime?.startsWith('video/')) {
    return strapiImage.previewUrl || '/logo.jpeg';
  }

  if (strapiImage.formats?.medium?.url) {
    return strapiImage.formats.medium.url.startsWith('http')
      ? strapiImage.formats.medium.url
      : `${strapiUrl}${strapiImage.formats.medium.url}`;
  }
  if (strapiImage.formats?.small?.url) {
    return strapiImage.formats.small.url.startsWith('http')
      ? strapiImage.formats.small.url
      : `${strapiUrl}${strapiImage.formats.small.url}`;
  }
  if (strapiImage.url) {
    return strapiImage.url.startsWith('http')
      ? strapiImage.url
      : `${strapiUrl}${strapiImage.url}`;
  }
  return '/logo.jpeg';
};

const DynamicPageContent = ({ page, lang = 'en' }) => {
  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams) || lang;
  const { t } = useTranslation(currentLang);

  // State for read more functionality
  const [isContentExpanded, setIsContentExpanded] = useState(false);

  // Function to check if content should show read more
  const shouldShowReadMore = (content) => {
    if (!content || !Array.isArray(content)) return false;

    // Count total text length
    const totalText = content
      .map(block =>
        block.children?.map(child => child.text).join(' ') || ''
      )
      .join(' ');

    return totalText.length > 300 || content.length > 3; // Show read more if more than 300 chars or 3 paragraphs
  };

  // Function to render content with read more functionality
  const renderContent = (content) => {
    if (!content || !Array.isArray(content)) return null;

    const showReadMore = shouldShowReadMore(content);
    const displayContent = showReadMore && !isContentExpanded
      ? content.slice(0, 2) // Show first 2 paragraphs when collapsed
      : content;

    return (
      <div>
        <div className="space-y-4">
          {displayContent.map((block, index) => {
            if (block.type === 'paragraph') {
              return (
                <p key={index} className="mb-4">
                  {block.children?.map((child, childIndex) => (
                    <span key={childIndex}>{child.text}</span>
                  ))}
                </p>
              );
            }
            return null;
          })}
        </div>

        {showReadMore && (
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsContentExpanded(!isContentExpanded)}
            className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
          >
            {isContentExpanded ? (
              <>
                {t('common.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
              </>
            ) : (
              <>
                {t('common.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
              </>
            )}
          </Button>
        )}
      </div>
    );
  };


  if (!page) {
    return <p>{t('common.noData')}</p>;
  }

  // Handle banner media (separate section)
  const bannerArray = Array.isArray(page.banner) ? page.banner : (page.banner ? [page.banner] : []);
  const bannerMediaList = bannerArray;
  const hasBannerMedia = bannerMediaList.length > 0;

  // Handle main content media from image field
  const imageArray = Array.isArray(page.image) ? page.image : (page.image ? [page.image] : []);
  const contentMediaList = imageArray;
  const hasContentMedia = contentMediaList.length > 0;

  // Handle Google Drive link (check both 'link' and 'drivelink' fields)
  const driveLink = page.link || page.drivelink;
  const hasDrive = driveLink && typeof driveLink === 'string' && driveLink.includes('drive.google.com');

  // Determine default tab priority: content media > banner media > drive
  const defaultTab = hasContentMedia ? 'content-media' : hasBannerMedia ? 'banner-media' : hasDrive ? 'drive' : 'none';

  // Check if we have any media at all
  const hasAnyMedia = hasContentMedia || hasBannerMedia || hasDrive;

  // Get hero image from banner field
  const heroImage = page.banner && Array.isArray(page.banner) && page.banner.length > 0
    ? getImageUrl(page.banner[0])
    : page.banner && page.banner.url
    ? getImageUrl(page.banner)
    : '/logo.jpeg';

  // Format date based on current language
  const formattedDate = page.date ? new Date(page.date).toLocaleDateString(
    currentLang === 'en' ? 'en-US' : currentLang === 'zh' ? 'zh-TW' : 'zh-CN',
    {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    }
  ) : null;

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={page.bannerTitle || page.title || "Page Title"}
        description={page.bannerSubtitle || "Learn more about our page."}
        image={heroImage}
        lang={currentLang}
      />

      <div className="container mx-auto py-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 items-start">
          <div className="prose prose-lg max-w-none">
            <h1 className="text-4xl md:text-5xl font-extrabold mb-2">{page.title}</h1>
            {formattedDate && (
              <p className="text-gray-500 text-sm mb-2">{formattedDate}</p>
            )}


            {renderContent(page.content)}
          </div>

          {hasAnyMedia && (
            <div className="mt-6 md:mt-0">
              <Tabs defaultValue={defaultTab} className="w-full">
                <TabsList className="flex w-full mb-2">
                  {hasContentMedia && <TabsTrigger value="content-media" className="flex-1">{t('mediaTab')}</TabsTrigger>}
                  {hasBannerMedia && <TabsTrigger value="banner-media" className="flex-1">{t('bannerTab')}</TabsTrigger>}
                  {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('driveTab')}</TabsTrigger>}
                </TabsList>
                {/* Content Media Tab */}
                {hasContentMedia && (
                  <TabsContent value="content-media">
                    <div className="relative aspect-w-16 aspect-h-9 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden rounded-lg shadow-sm">
                      {contentMediaList.length > 1 ? (
                        <EnhancedMediaSlider
                          media={contentMediaList}
                          alt={page.title || 'Content Media'}
                          className="!aspect-[16/9] absolute inset-0 w-full h-full"
                          interval={3000}
                        />
                      ) : (
                        contentMediaList[0] && (contentMediaList[0].mime?.startsWith('video/') ? (
                          <video
                            src={contentMediaList[0].url}
                            poster={contentMediaList[0].previewUrl || undefined}
                            controls
                            className="w-full h-full object-contain"
                            playsInline
                          />
                        ) : (
                          <Image
                            src={getImageUrl(contentMediaList[0])}
                            alt={contentMediaList[0].alternativeText || page.title || 'Content Media'}
                            fill
                            className="object-contain"
                            sizes="(max-width: 768px) 100vw, 50vw"
                          />
                        ))
                      )}
                    </div>
                  </TabsContent>
                )}

                {/* Banner Media Tab */}
                {hasBannerMedia && (
                  <TabsContent value="banner-media">
                    <div className="relative aspect-w-16 aspect-h-9 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden rounded-lg shadow-sm">
                      {bannerMediaList.length > 1 ? (
                        <EnhancedMediaSlider
                          media={bannerMediaList}
                          alt={page.title || 'Banner Media'}
                          className="!aspect-[16/9] absolute inset-0 w-full h-full"
                          interval={3000}
                        />
                      ) : (
                        bannerMediaList[0] && (bannerMediaList[0].mime?.startsWith('video/') ? (
                          <video
                            src={bannerMediaList[0].url}
                            poster={bannerMediaList[0].previewUrl || undefined}
                            controls
                            className="w-full h-full object-contain"
                            playsInline
                          />
                        ) : (
                          <Image
                            src={getImageUrl(bannerMediaList[0])}
                            alt={bannerMediaList[0].alternativeText || page.title || 'Banner Media'}
                            fill
                            className="object-contain"
                            sizes="(max-width: 768px) 100vw, 50vw"
                          />
                        ))
                      )}
                    </div>
                  </TabsContent>
                )}
                {/* Google Drive Tab */}
                {hasDrive && (
                  <TabsContent value="drive">
                    <div className="relative aspect-w-16 aspect-h-9 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden rounded-lg shadow-sm">
                      <GoogleDriveEmbed videoId={driveLink} title={page.title || ''} className="!aspect-h-9 absolute inset-0 w-full h-full rounded-lg" />
                    </div>
                  </TabsContent>
                )}
              </Tabs>
            </div>
          )}
          {!hasAnyMedia && (
            <div className="md:col-span-1 flex items-center justify-center aspect-[4/3] bg-gray-100 rounded-lg shadow-sm">
              <p className="text-muted-foreground p-4 text-center">{t('common.noMedia')}</p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default DynamicPageContent;