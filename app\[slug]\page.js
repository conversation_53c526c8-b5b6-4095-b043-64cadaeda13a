import axios from 'axios';
import { notFound } from 'next/navigation';
import DynamicPageContent from './DynamicPageContent';

const STRAPI_API_URL = process.env.NEXT_PUBLIC_STRAPI_URL;
export const revalidate = 60;

// Force dynamic rendering for this page since we use searchParams
export const dynamic = 'force-dynamic';

// Map language codes to Strapi locales
function getStrapiLocale(lang) {
  switch (lang) {
    case 'zh':
      return 'zh-Hant-HK'; // Traditional Chinese
    case 'cn':
      return 'zh-Hans-HK'; // Simplified Chinese
    case 'en':
      return 'en'; // English
    default:
      return 'en'; // Default to English
  }
}

export async function generateStaticParams() {
  try {
    // Fetch pages for all locales to get all unique slugs
    const locales = ['en', 'zh-Hant-HK', 'zh-Hans-HK'];
    const allSlugs = new Set();

    for (const locale of locales) {
      try {
        const res = await axios.get(`${STRAPI_API_URL}/new-pages`, {
          params: {
            'fields': 'Slug',
            'locale': locale,
            'pagination[pageSize]': 100, // Get more pages
          },
        });

        const pages = res.data.data;
        if (pages && Array.isArray(pages)) {
          pages.forEach(page => {
            if (page && page.Slug) {
              allSlugs.add(page.Slug);
            }
          });
        }
      } catch (localeError) {
        console.warn(`Error fetching pages for locale ${locale}:`, localeError.message);
      }
    }

    // Convert Set to array of slug objects
    const staticPaths = Array.from(allSlugs).map(slug => ({ slug }));

   
    return staticPaths;

  } catch (error) {
    console.error(
      'Error fetching static params for build:',
      error.message,
      'Status:', error.response?.status,
      'Data:', JSON.stringify(error.response?.data, null, 2)
    );
    return [];
  }
}

async function getPageData(slugValueFromUrl, locale = 'en') {
  try {
    const strapiLocale = getStrapiLocale(locale);

    const res = await axios.get(`${STRAPI_API_URL}/new-pages`, {
      params: {
        'filters[Slug][$eq]': slugValueFromUrl,
        'populate': '*',
        'locale': strapiLocale,
      },
    });

    const pageDataArray = res.data.data;

    if (!pageDataArray || !Array.isArray(pageDataArray) || pageDataArray.length === 0) {
      return null;
    }

    const pageData = pageDataArray[0];

    // If no data found for the requested locale, try to get the default English version
    if (!pageData && locale !== 'en') {
      return await getPageData(slugValueFromUrl, 'en');
    }

    return pageData;
  } catch (error) {
    console.error(
      `Error fetching page data for slug ${slugValueFromUrl} with locale ${locale}:`,
      error.message,
      'Status:', error.response?.status,
      'Data:', JSON.stringify(error.response?.data, null, 2)
    );

    // If error and not already trying English, try English fallback
    if (locale !== 'en') {
      return await getPageData(slugValueFromUrl, 'en');
    }

    return null;
  }
}

export default async function Page({ params, searchParams }) {
  // Get language from search params (e.g., ?lang=zh)
  const lang = searchParams?.lang || 'en';

  const page = await getPageData(params.slug, lang);

  if (!page) {
    notFound();
  }

  return <DynamicPageContent page={page} lang={lang} />;
}