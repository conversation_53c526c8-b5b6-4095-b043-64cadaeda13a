"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { AffiliatedAssociation, fetchAffiliatedAssociations } from "@/lib/strapi"

// Loading component
function AffiliatedAssociationsLoading() {
  return (
    <div className="min-h-screen">
      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-16">
          {Array(3).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-10 items-start">
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                  <div className="aspect-[16/12] bg-muted rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Main content component
function AffiliatedAssociationsContent() {
  const searchParams = useSearchParams()
  const [associations, setAssociations] = useState<AffiliatedAssociation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return undefined
    }
  }, [currentLang])

  useEffect(() => {
    let cancelled = false;
    async function fetchData() {
      try {
        setLoading(true)
        const response = await fetchAffiliatedAssociations({
          locale: getStrapiLocale,
          populate: '*',
          pagination: { pageSize: 5000 }
        })
        
        if (!cancelled) {
          if (response.data) {
            setAssociations(response.data)
          } else {
            setError('No associations found')
          }
        }
      } catch (error) {
        if (!cancelled) {
          setError('Failed to fetch')
        }
      } finally {
        if (!cancelled) {
          setLoading(false)
        }
      }
    }
    fetchData()
    return () => { cancelled = true }
  }, [getStrapiLocale])

  if (loading) {
    return (
      <div className="w-full max-w-7xl mx-auto py-12">
        <div className="flex items-center justify-center min-h-[60vh]">
          <div className="w-full h-[80vh] bg-gray-100 animate-pulse" />
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="w-full max-w-7xl mx-auto py-12">
        <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
          <h2 className="text-xl font-semibold text-red-700 mb-2">
            {error === 'No associations found' ? t('affiliatedAssociation.noAssociations') : t('affiliatedAssociation.error')}
          </h2>
        </div>
      </div>
    )
  }

  if (associations.length === 0) {
    return (
      <div className="w-full max-w-7xl mx-auto py-12">
        <div className="text-center">
          <p className="text-muted-foreground">
            {t('affiliatedAssociation.noAssociations')}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-7xl mx-auto py-12">
      <div className="flex flex-col gap-8">
        {associations
          .slice()
          .sort((a, b) => parseInt(a.pageno || '0') - parseInt(b.pageno || '0'))
          .map((association) => (
            <div key={association.id} className="w-full">
              {association.image && association.image.length > 0 && (
                <div className="flex flex-col gap-8">
                  {association.image.map((img, imgIdx) => (
                    <Image
                      key={img.id}
                      src={img.formats?.large?.url || img.url}
                      alt={img.name || `Affiliated Association Image ${imgIdx + 1}`}
                      width={img.width}
                      height={img.height}
                      className="object-contain w-full"
                      priority={imgIdx === 0}
                      sizes="(max-width: 768px) 100vw, 80vw"
                    />
                  ))}
                </div>
              )}
            </div>
          ))}
      </div>
    </div>
  )
}

// Component that uses searchParams
function AffiliatedAssociationsPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="affiliatedAssociation.title"
        description="affiliatedAssociation.description"
        image="/logo.jpeg"
        lang={currentLang}
      />

      <Suspense fallback={<AffiliatedAssociationsLoading />}>
        <AffiliatedAssociationsContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function AffiliatedAssociations() {
  return (
    <Suspense fallback={<AffiliatedAssociationsLoading />}>
      <AffiliatedAssociationsPage />
    </Suspense>
  )
}