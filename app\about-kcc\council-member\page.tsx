"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { fetchFromStrapi } from "@/lib/strapi"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface StrapiImageFormat {
  ext: string
  url: string
  hash: string
  mime: string
  name: string
  path: string | null
  size: number
  width: number
  height: number
  sizeInBytes: number
  provider_metadata: {
    public_id: string
    resource_type: string
  }
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText: string | null
  caption: string | null
  width: number
  height: number
  formats: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl: string | null
  provider: string
  provider_metadata: {
    public_id: string
    resource_type: string
  }
  createdAt: string
  updatedAt: string
  publishedAt: string
}

interface CouncilMemberData {
  id: number
  documentId?: string
  title?: string
  name?: string
  position?: string
  biography?: any[]
  description?: any[]
  order?: number
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  locale?: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

interface StrapiResponse {
  data: CouncilMemberNews[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

interface CouncilMemberNews {
  id: number
  documentId: string
  pageno: string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image: StrapiImage[]
  localizations: Array<{
    id: number
    documentId: string
    pageno: string
    createdAt: string
    updatedAt: string
    publishedAt: string
    locale: string
  }>
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

function CouncilMemberContent() {
  const searchParams = useSearchParams()
  const [memberNews, setMemberNews] = useState<CouncilMemberNews[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  useEffect(() => {
    const loadMemberNews = async () => {
      try {
        setLoading(true)
        const response = await fetchFromStrapi<CouncilMemberNews[]>('/council-member-news', {
          populate: '*',
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        })

        if (response.data) {
          // Sort by page number and remove duplicates based on documentId
          const uniqueData = response.data.reduce((acc: CouncilMemberNews[], current) => {
            const exists = acc.find(item => item.documentId === current.documentId)
            if (!exists) {
              acc.push(current)
            }
            return acc
          }, [])
          
          const sortedData = uniqueData.sort((a, b) => parseInt(a.pageno) - parseInt(b.pageno))
          setMemberNews(sortedData)
        }
      } catch (err: any) {
        console.error("Error fetching council member news:", err)
        setError(err.message || "Failed to load council member news")
      } finally {
        setLoading(false)
      }
    }

    loadMemberNews()
  }, [getStrapiLocale])

  if (error) {
    return (
      <div className="w-full max-w-7xl mx-auto py-12">
        <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
          <h2 className="text-xl font-semibold text-red-700 mb-2">
            {error}
          </h2>
        </div>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="w-full max-w-7xl mx-auto py-12">
        <div className="flex items-center justify-center min-h-[60vh]">
          <Skeleton className="w-full h-[80vh]" />
        </div>
      </div>
    )
  }

  if (memberNews.length === 0) {
    return (
      <div className="w-full max-w-7xl mx-auto py-12">
        <div className="text-center">
          <p className="text-muted-foreground">
            {t('councilMember.noMembers')}
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="w-full max-w-7xl mx-auto py-12">
      <div className="flex flex-col gap-8">
        {memberNews.map((news, index) => (
          <div key={news.documentId} className="w-full">
            {news.image && news.image.length > 0 && (
              <Image
                src={news.image[0].formats?.large?.url || 
                     news.image[0].url || 
                     '/logo.jpeg'}
                alt={`Council Member Page ${news.pageno}`}
                width={news.image[0].width || 1000}
                height={news.image[0].height || 1000}
                className="object-contain w-full"
                priority={index === 0}
                sizes="(max-width: 768px) 100vw, 80vw"
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

function CouncilMemberLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative h-96 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="h-8 bg-white/20 rounded animate-pulse w-64 mx-auto mb-4" />
          <div className="h-4 bg-white/20 rounded animate-pulse w-96 mx-auto" />
        </div>
      </div>

      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array(6).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="aspect-video bg-muted animate-pulse" />
                <div className="p-6 space-y-4">
                  <div className="h-6 bg-muted rounded animate-pulse w-3/4" />
                  <div className="h-3 bg-muted rounded animate-pulse w-1/2" />
                  <div className="space-y-2">
                    <div className="h-2 bg-muted rounded animate-pulse" />
                    <div className="h-2 bg-muted rounded animate-pulse w-5/6" />
                    <div className="h-2 bg-muted rounded animate-pulse w-4/6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Component that uses searchParams
function CouncilMemberPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <>
      <AnimatedHero
        title="councilMember.title"
        description="councilMember.description"
        image="/logo.jpeg"
        lang={currentLang}
      />

      <Suspense fallback={<CouncilMemberLoading />}>
        <CouncilMemberContent />
      </Suspense>
    </>
  )
}

// Main export with Suspense wrapper
export default function CouncilMember() {
  return (
    <Suspense fallback={
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "理监事会名单",
            en: "Council Members",
          }}
          description={{
            zh: "认识九龙总商会的理监事会成员",
            en: "Meet the board members of the Kowloon Chamber of Commerce",
          }}
          image="/logo.jpeg"
        />
        <div className="container py-12">
          <div className="flex items-center justify-center min-h-[60vh]">
            <Skeleton className="w-full h-[80vh]" />
          </div>
        </div>
      </div>
    }>
      <CouncilMemberPage />
    </Suspense>
  )
}