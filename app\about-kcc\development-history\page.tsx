'use client'
import { Suspense, useState, useEffect, useMemo } from 'react'
import { useSearchParams } from 'next/navigation'
import AnimatedHero from '@/components/animated-hero'
import PdfViewer from '@/components/pdf-viewer'
import { fetchKCCDevelopmentHistories } from '@/lib/strapi'
import { getTranslation, getLanguageFromParams } from '@/lib/translations'
import { OptimizedPdfLoader } from '@/lib/optimized-pdf-loader'

function DevelopmentHistoryLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Loading...</p>
    </div>
  )
}

function DevelopmentHistoryPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const [drivelink, setDrivelink] = useState<string | undefined>(undefined)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Map currentLang to Strapi locale
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return undefined
    }
  }, [currentLang])

  useEffect(() => {
    async function fetchData() {
      setLoading(true)
      setError(null)
      try {
        const histories = await fetchKCCDevelopmentHistories({ locale: getStrapiLocale })
        const backendLink = histories[0]?.drivelink
        // Fallback to local PDF if backend link is missing or empty
        setDrivelink(backendLink && backendLink.trim() ? backendLink : '/pdf/KCC Development  KCC 75 年發展史_revised (1).pdf')
      } catch (e) {
        setError(getTranslation('developmentHistory.error', currentLang))
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [getStrapiLocale, currentLang])

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: getTranslation('developmentHistory.title', 'zh'),
          cn: getTranslation('developmentHistory.title', 'cn'),
          en: getTranslation('developmentHistory.title', 'en'),
        }}
        description={{
          zh: getTranslation('developmentHistory.description', 'zh'),
          cn: getTranslation('developmentHistory.description', 'cn'),
          en: getTranslation('developmentHistory.description', 'en'),
        }}
        image="/logo.jpeg"
        lang={currentLang}
      />
      {loading ? (
        <DevelopmentHistoryLoading />
      ) : error ? (
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>{error}</p>
        </div>
      ) : !drivelink ? (
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>{getTranslation('developmentHistory.error', currentLang) || 'PDF not available.'}</p>
        </div>
      ) : (
        <div className="relative z-0">
          <PdfViewer pdfUrl={drivelink} />
        </div>
      )}
    </div>
  )
}

export default function DevelopmentHistoryPage() {
  return (
    <Suspense fallback={<DevelopmentHistoryLoading />}>
      <DevelopmentHistoryPageContent />
    </Suspense>
  )
}