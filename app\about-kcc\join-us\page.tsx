"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import FullImageSlider from "@/components/full-image-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  url: string
  mime?: string
  previewUrl?: string
  formats?: StrapiImageFormats
}

interface JoinUsData {
  id: number;
  documentId: string;
  title: string;
  requirements?: any[];
  benefits?: any[];
  applicationProcess?: any[];
  content?: any[];
  description?: any[];
  website?: string;
  order?: string;
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
}

// Loading component
function JoinUsLoading() {
  return (
    <div className="min-h-screen">
      <div className="container py-12 space-y-8">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="text-center space-y-4">
              <div className="h-6 bg-muted rounded animate-pulse w-48 mx-auto" />
              <div className="space-y-2">
                <div className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                <div className="h-4 bg-muted rounded animate-pulse w-4/6 mx-auto" />
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="space-y-16">
          {Array(2).fill(null).map((_, index) => (
            <Card key={`loading-${index}`} className="overflow-hidden">
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-10 items-start">
                  <div className="space-y-4">
                    <div className="h-8 bg-muted rounded animate-pulse w-3/4" />
                    <div className="space-y-2">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                  <div className="aspect-[16/12] bg-muted rounded animate-pulse" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Main content component
function JoinUsContent() {
  const searchParams = useSearchParams()
  const [joinUsData, setJoinUsData] = useState<JoinUsData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set())
  const [expandedApplicationProcess, setExpandedApplicationProcess] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (itemId: number) => {
    setExpandedItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  const toggleApplicationProcess = (itemId: number) => {
    setExpandedApplicationProcess(prev => {
      const newSet = new Set(prev)
      if (newSet.has(itemId)) {
        newSet.delete(itemId)
      } else {
        newSet.add(itemId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api';
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e045a02cb0aee7bca76af467aa8b93ae';

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/join-uses`)
        url.searchParams.append('populate', '*')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        setDebugInfo("Fetching data from API...");

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`
          }
        });

        if (!response.ok) {
          throw new Error(`API returned ${response.status}: ${response.statusText}`);
        }

        const data = await response.json();
        setDebugInfo(`API Response received. Looking for joinUsSection...`);

        // Based on the API response structure
        if (data && data.data && Array.isArray(data.data) && data.data.length > 0) {
          
          setDebugInfo(`Found ${data.data.length} join us items.`);
          setJoinUsData(data.data);
        } else {
          setDebugInfo(`Join us data not found in response: ${JSON.stringify(data).substring(0, 100)}...`);
          setError(t('joinUs.noData'));
        }
      } catch (error: any) {
        console.error('Error fetching join us data:', error);
        setDebugInfo(`Error: ${error.message}`);
        setError(t('joinUs.error'));
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, [currentLang, getStrapiLocale]); // Re-run when language changes

  // Helper function to render rich text content
  const renderRichText = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('joinUs.noContent')}</p>
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children?.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    })
  }

  // Function to render list items
  const renderList = (items: any[]) => {
    if (!items || !Array.isArray(items) || items.length === 0) {
      return <p className="text-muted-foreground">{t('joinUs.noContent')}</p>;
    }

    // Filter out empty items
    const validItems = items.filter(item => {
      if (item.type === "paragraph") {
        const text = item.children?.map((child: any) => child.text).join('').trim();
        return text && text.length > 0;
      }
      return item && item.toString().trim().length > 0;
    });

    if (validItems.length === 0) {
      return <p className="text-muted-foreground">{t('joinUs.noContent')}</p>;
    }

    return (
      <ul className="list-disc pl-5 space-y-2">
        {validItems.map((item, index) => {
          const content = item.type === "paragraph" ?
            item.children?.map((child: any) => child.text).join('') :
            item;

          return (
            <li key={index}>
              {content}
            </li>
          );
        })}
      </ul>
    );
  }

  // Helper function to get images for ImageSlider
  const getJoinUsImages = (item: JoinUsData): StrapiImage[] => {
    const images: StrapiImage[] = []

    // Check if image is an array (multiple images)
    if (Array.isArray(item.image)) {
      images.push(...item.image)
    } else if (item.image) {
      // Single image
      images.push(item.image)
    }

    // Also check images field
    if (item.images && Array.isArray(item.images)) {
      images.push(...item.images)
    }

    return images
  }

  if (loading) {
    return <JoinUsLoading />
  }

  // Show a neutral message if there is no data
  if (!joinUsData || joinUsData.length === 0) {
    return (
      <div className="container py-12">
        <div className="text-center text-muted-foreground py-24">
          {t('joinUs.noData')}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="container py-12">
        <Card>
          <CardContent className="p-8">
            <div className="text-center">
              <p className="text-red-500">{error}</p>
              {debugInfo && (
                <p className="text-sm text-muted-foreground mt-2">{debugInfo}</p>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <div className="container py-12 space-y-8">
        {joinUsData.map((item) => (
          <Card key={item.id} className="overflow-hidden">
            <CardContent className="p-8">
              <div className="grid md:grid-cols-2 gap-10 items-start">
                <div className="space-y-6">
                  <h2 className="text-2xl font-bold">{item.title}</h2>
                  {item.description && renderRichText(item.description)}
                  {item.requirements && (
                    <div>
                      <h3 className="text-xl font-semibold mb-4">{t('joinUs.requirements')}</h3>
                      {renderList(item.requirements)}
                    </div>
                  )}
                  {item.benefits && (
                    <div>
                      <h3 className="text-xl font-semibold mb-4">{t('joinUs.benefits')}</h3>
                      {renderList(item.benefits)}
                    </div>
                  )}
                  {item.applicationProcess && (
                    <div>
                      <h3 className="text-xl font-semibold mb-4">{t('joinUs.applicationProcess')}</h3>
                      {renderList(item.applicationProcess)}
                    </div>
                  )}
                  {item.website && (
                    <div className="mt-4">
                      <a
                        href={item.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-primary hover:text-primary/80"
                      >
                        {t('joinUs.visitWebsite')}
                      </a>
                    </div>
                  )}
                </div>
                <div>
                  {(() => {
                    const images = getJoinUsImages(item)
                    if (images.length === 0) {
                      return (
                        <div className="aspect-[4/3] bg-gray-100 flex items-center justify-center">
                          <p className="text-muted-foreground">{t('joinUs.noImage')}</p>
                        </div>
                      )
                    }
                    return (
                      <FullImageSlider
                        images={images}
                        alt={item.title || t('joinUs.title')}
                      />
                    )
                  })()}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// New client component to handle AnimatedHero and Suspense for main content
function JoinUsClientPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="joinUs.title"
        description="joinUs.description"
        lang={currentLang}
      />
      <Suspense fallback={<JoinUsLoading />}>
        <JoinUsContent />
      </Suspense>
    </div>
  )
}

// Main page component using Suspense for the client component
export default function JoinUsPage() {
  return (
    <Suspense fallback={<JoinUsLoading />}>
      <JoinUsClientPage />
    </Suspense>
  )
}