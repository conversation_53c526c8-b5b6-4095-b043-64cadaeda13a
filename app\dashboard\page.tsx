"use client"

import { useEffect } from "react"
import { useUser } from "@/contexts/user-context"
import { useRouter, useSearchParams } from "next/navigation"
import { UserEvents } from "@/components/user-events"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { Suspense } from "react";

function DashboardPageContent() {
  const { user } = useUser()
  const router = useRouter()
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  useEffect(() => {
    if (!user) {
      router.push(`/login?lang=${currentLang}`)
    }
  }, [user, router, currentLang])

  if (!user) {
    return null
  }

  return (
    <div className="container py-8 px-2 sm:px-4">
      <h1 className="text-2xl sm:text-3xl font-bold mb-6 text-center md:text-left">
        <span className="block mb-1">{t("dashboard.title")}</span>
        <span className="text-lg sm:text-xl text-gray-600">{t("dashboard.subtitle")}</span>
      </h1>

      <div className="space-y-8">
        {/* Profile Information */}
        <div className="bg-white shadow-md rounded-lg p-4 md:p-8 border border-gray-100">
          <div className="space-y-6">
            <div>
              <h2 className="text-lg sm:text-xl font-semibold mb-4 text-center md:text-left">
                <span className="block">{t("dashboard.profileTitle")}</span>
                <span className="text-sm sm:text-base text-gray-600">{t("dashboard.profileSubtitle")}</span>
              </h2>

              <div className="mt-4 space-y-4 pl-0 sm:pl-2 text-center md:text-left">
                <p className="flex flex-col md:flex-row md:items-center gap-1 md:gap-3">
                  <span className="font-medium text-gray-700">{t("dashboard.username")}:</span>
                  <span className="text-gray-900">{user.username}</span>
                </p>
                <p className="flex flex-col md:flex-row md:items-center gap-1 md:gap-3">
                  <span className="font-medium text-gray-700">{t("dashboard.email")}:</span>
                  <span className="text-gray-900">{user.email}</span>
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* User Events */}
        <UserEvents />
      </div>
    </div>
  )
}

export default function DashboardPage() {
  return (
    <Suspense fallback={<div>Loading dashboard...</div>}>
      <DashboardPageContent />
    </Suspense>
  );
}