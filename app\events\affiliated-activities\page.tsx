'use client'

import { useState, useEffect, useMemo, Suspense } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Building, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import AnimatedHero from '@/components/animated-hero'
// Import necessary types and fetch function
import { fetchExpoActivitiesData, type ExpoActivity } from '@/lib/strapi'
// import ImageSlider from '@/components/image-slider' // Remove ImageSlider import
import EnhancedMediaSlider from '@/components/enhanced-media-slider' // Import EnhancedMediaSlider
import { useTranslation, getLanguageFromParams, type TranslationKey } from '@/lib/translations' // Import TranslationKey
import { useSearchParams } from 'next/navigation'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import { <PERSON><PERSON>, <PERSON><PERSON>List, Ta<PERSON>Trigger, TabsContent } from '@/components/ui/tabs'
import YouTubeEmbed from '@/components/ui/youtube-embed'

// Component for handling expandable text
function ExpandableText({ text, maxLength = 150, t }: { text: string; maxLength?: number; t: (key: TranslationKey) => string }) { // Update t prop type
  const [isExpanded, setIsExpanded] = useState(false)

  if (text.length <= maxLength) {
    return <div className="text-muted-foreground">{text}</div>
  }

  return (
    <div className="text-muted-foreground">
      <div>
        {isExpanded ? text : `${text.substring(0, maxLength)}...`}
      </div>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => setIsExpanded(!isExpanded)}
        className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
      >
        {isExpanded ? (
          <>
            <ChevronUp className="h-4 w-4 mr-1" />
            {t('affiliatedActivities.readLess' as TranslationKey)} {/* Cast key */}
          </>
        ) : (
          <>
            <ChevronDown className="h-4 w-4 mr-1" />
            {t('common.readMore' as TranslationKey)} {/* Cast key */}
          </>
        )}
      </Button>
    </div>
  )
}

// Main component with Suspense wrapper
export default function AffiliatedActivitiesPage() {
  return (
    <Suspense fallback={<AffiliatedActivitiesLoading />}>
      <AffiliatedActivitiesContent />
    </Suspense>
  )
}

function AffiliatedActivitiesContent() {
  const [activities, setActivities] = useState<ExpoActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  useEffect(() => {
    let isMounted = true

    const loadExpoActivities = async () => {
      try {
        setLoading(true)
        setError(null)
      

        const expoActivitiesData = await fetchExpoActivitiesData({
          populate: "*",
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        })

        if (!isMounted) return

        if (expoActivitiesData && expoActivitiesData.length > 0) {
          // Filter out duplicate entries based on documentId
          const uniqueActivities = Array.from(new Map(expoActivitiesData.map(item => [item.documentId, item])).values());

          // Sort unique activities by date descending (most recent first), fallback to createdAt
          const sortedActivities = [...uniqueActivities].sort((a, b) => {
            const dateA = new Date(a.date || a.createdAt || 0).getTime();
            const dateB = new Date(b.date || b.createdAt || 0).getTime();
            return dateB - dateA;
          })
          setActivities(sortedActivities)
        } else {
          // Fallback without locale
          try {
            const fallbackResponse = await fetchExpoActivitiesData({ populate: '*', pagination: { pageSize: 5000 } })

            if (!isMounted) return

            if (fallbackResponse && fallbackResponse.length > 0) {
               // Filter out duplicate entries based on documentId for fallback
              const uniqueFallbackActivities = Array.from(new Map(fallbackResponse.map(item => [item.documentId, item])).values());

              const sortedFallback = [...uniqueFallbackActivities].sort((a, b) => {
                const dateA = new Date(a.date || a.createdAt || 0).getTime();
                const dateB = new Date(b.date || b.createdAt || 0).getTime();
                return dateB - dateA;
              })
              setActivities(sortedFallback)
            } else {
              setError(t('affiliatedActivities.noActivities' as TranslationKey)) // Cast key
            }
          } catch (fallbackErr) {
            if (isMounted) {
              setError(t('affiliatedActivities.noActivities' as TranslationKey)) // Cast key
            }
          }
        }
      } catch (err) {
        if (isMounted) {
          console.error('Error fetching expo activities:', err)
          setError(t('affiliatedActivities.error' as TranslationKey)) // Cast key
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    loadExpoActivities()

    return () => {
      isMounted = false
    }
  }, [currentLang, getStrapiLocale])

  // Define the title and description objects for AnimatedHero with translations for each language
  const heroTitle = useMemo(() => ({
    en: t('affiliatedActivities.staticTitle' as TranslationKey) as string,
    zh: t('affiliatedActivities.staticTitle' as TranslationKey) as string,
    cn: t('affiliatedActivities.staticTitle' as TranslationKey) as string,
  }), [t]);

  const heroDescription = useMemo(() => ({
    en: t('affiliatedActivities.staticDescription' as TranslationKey) as string,
    zh: t('affiliatedActivities.staticDescription' as TranslationKey) as string,
    cn: t('affiliatedActivities.staticDescription' as TranslationKey) as string,
  }), [t]);


  return (
    <div className={`min-h-screen bg-gradient-to-br from="blue-50" to="indigo-100"`}> {/* Using template literal */}
      <AnimatedHero
        title={heroTitle} // Use the memoized heroTitle object
        description={heroDescription} // Use the memoized heroDescription object
        image="/logo.jpeg"
        lang={currentLang}
      />

      <div className="container py-12">
        {loading && <AffiliatedActivitiesLoading />}

        {error && (
          <div className="text-center text-red-500 my-8">
            <p>{error}</p>
          </div>
        )}

        {!loading && !error && activities.length === 0 && (
          <div className="text-center text-muted-foreground my-8">
            <p>{t('affiliatedActivities.noContent' as TranslationKey)} {/* Cast key */}</p>
          </div>
        )}

        {!loading && !error && activities.length > 0 && (
          <div className="grid md:grid-cols-2 gap-6">
            {activities.map((activity, index) => {
              const mediaItems = Array.isArray(activity.image) ? activity.image : (activity.image ? [activity.image] : []);
              const drivelink = (activity as any).drivelink ?? '';
              const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
              const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));
              const hasDrive = isGoogleDriveLink(drivelink);
              const hasYouTube = isYouTubeLink(drivelink);
              const hasMedia = mediaItems.length > 0;
              const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';
              return (
                <Card key={`activity-${index}`} className="overflow-hidden">
                  {(hasMedia || hasDrive || hasYouTube) && (
                    <Tabs defaultValue={defaultTab} className="w-full">
                      <TabsList className="flex w-full mb-2">
                        {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                        {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                        {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                      </TabsList>
                      {hasMedia && (
                        <TabsContent value="media">
                          <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden">
                            {mediaItems.length > 1 ? (
                              <EnhancedMediaSlider
                                media={mediaItems}
                                alt={(activity as any).expo_name || t('affiliatedActivities.defaultMediaAlt' as TranslationKey)}
                              />
                            ) : (
                              mediaItems[0].mime?.startsWith('video/') ? (
                                <video
                                  src={mediaItems[0].url}
                                  poster={mediaItems[0].previewUrl || undefined}
                                  controls
                                  className="w-full h-full object-contain"
                                  playsInline
                                />
                              ) : (
                                <Image
                                  src={mediaItems[0].url || '/logo.jpeg'}
                                  alt={(activity as any).expo_name || t('affiliatedActivities.defaultMediaAlt' as TranslationKey)}
                                  fill
                                  className="object-contain"
                                  sizes="(max-width: 768px) 100vw, 33vw"
                                />
                              )
                            )}
                          </div>
                        </TabsContent>
                      )}
                      {hasDrive && (
                        <TabsContent value="drive">
                          <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                            <GoogleDriveEmbed videoId={drivelink} title={(activity as any).expo_name || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                          </div>
                        </TabsContent>
                      )}
                      {hasYouTube && (
                        <TabsContent value="youtube">
                          <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                            <YouTubeEmbed videoId={drivelink} title={(activity as any).expo_name || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                          </div>
                        </TabsContent>
                      )}
                    </Tabs>
                  )}
                  <CardContent className="p-6">
                    <h3 className="text-xl font-semibold mb-2">{activity.expo_name}</h3>
                    {activity.date && (
                      <div className="flex items-center text-muted-foreground mb-2">
                        <Calendar className="h-4 w-4 mr-2" />
                        <time dateTime={activity.date}>
                          {new Date(activity.date).toLocaleDateString(
                            currentLang === 'en' ? 'en-US' : 'zh-HK',
                            { year: 'numeric', month: 'long', day: 'numeric' }
                          )}
                        </time>
                      </div>
                    )}
                    {activity.location && (
                      <div className="flex items-center text-muted-foreground mb-4">
                        <MapPin className="h-4 w-4 mr-2" />
                        <span>{activity.location}</span>
                      </div>
                    )}
                    {activity.description && (
                      <ExpandableText
                        text={String(activity.description)}
                        maxLength={150}
                        t={t}
                      />
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>
    </div>
  )
}

// Loading component
function AffiliatedActivitiesLoading() {
   // Get translation function for loading component
   const { t } = useTranslation(); // Use useTranslation hook
  return (
    <div className={`min-h-screen bg-gradient-to-br from="blue-50" to="indigo-100"`}> {/* Using template literal */}
      <div className="w-full aspect-video bg-gray-200 animate-pulse"></div>
      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="space-y-4 text-center">
              <Skeleton className="h-8 w-3/4 mx-auto" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>

        <div className="grid md:grid-cols-2 gap-6">
          {Array(4).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <div className="relative aspect-video">
                <Skeleton className="absolute inset-0" />
              </div>
              <CardContent className="p-6 space-y-4">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}