'use client'

import { useState, useEffect, useMemo, Suspense, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchBloodDonations, type BloodDonation } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// Define media item interface for enhanced media slider
interface CareTeamMediaItem {
  id: number
  url: string
  mime?: string
  previewUrl?: string | null
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: {
    large?: { url: string }
    medium?: { url: string }
    small?: { url: string }
    thumbnail?: { url: string }
  }
}

// Component that uses searchParams
function CareTeamContent() {
  const [activities, setActivities] = useState<BloodDonation[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  const toggleExpanded = useCallback((id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }, [])

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = useCallback((content: string | undefined) => {
    return content && content.length > 100;
  }, [])

  // Helper function to get activity media items compatible with EnhancedMediaSlider
  const getActivityMedia = useCallback((activity: BloodDonation): CareTeamMediaItem[] => {
    const mediaItems: CareTeamMediaItem[] = [];
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

    if (activity.image && Array.isArray(activity.image)) {
      activity.image.forEach((img) => {
        if (img && img.url) {
          mediaItems.push({
            id: img.id,
            url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`
          });
        }
      });
    }

    return mediaItems;
  }, [])

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
       

        const activitiesData = await fetchBloodDonations({
          populate: '*',
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        });

       

        if (isMounted) {
          if (activitiesData && activitiesData.length > 0) {
            // Sort activities by date descending (most recent first), fallback to createdAt
            const sortedActivities = [...activitiesData].sort((a, b) => {
              const dateA = new Date(a.date || a.createdAt || 0).getTime();
              const dateB = new Date(b.date || b.createdAt || 0).getTime();
              return dateB - dateA;
            });
            setActivities(sortedActivities);
            setError(null);
          } else {
            setActivities([]);
            setError(null); // Don't set error for empty data
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching care team activities:', err);
          setError('An error occurred while fetching care team activities data.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [currentLang, getStrapiLocale]) // Removed 't' from dependencies to prevent infinite loop

  if (error) {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title={{
            zh: t('careTeam.title'),
            en: t('careTeam.title'),
          }}
          description={{
            zh: t('careTeam.description'),
            en: t('careTeam.description'),
          }}
          image="/logo.jpeg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {error}
            </h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: t('careTeam.title'),
          en: t('careTeam.title'),
        }}
        description={{
          zh: t('careTeam.description'),
          en: t('careTeam.description'),
        }}
        image="/logo.jpeg"
      />

      <div className="container py-12">
        {/* Care Team Activities Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Care Team Activities Items */}
              {activities.length > 0 ? (
                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const content = activity.description
                      const showReadMore = shouldShowReadMore(content)
                      const mediaItems = getActivityMedia(activity)
                      // Use 'link' for Drive/YouTube, fallback to drivelink/youtubelink if needed
                      const link = (activity as any).link || (activity as any).drivelink || (activity as any).youtubelink || '';
                      const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
                      const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));
                      const hasDrive = isGoogleDriveLink(link);
                      const hasYouTube = isYouTubeLink(link);
                      const hasMedia = mediaItems.length > 0;
                      const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';

                      return (
                        <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {(hasMedia || hasDrive || hasYouTube) && (
                            <Tabs defaultValue={defaultTab} className="w-full">
                              <TabsList className="flex w-full mb-2">
                                {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                                {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                                {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                              </TabsList>
                              {hasMedia && (
                                <TabsContent value="media">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden">
                                    {mediaItems.length > 1 ? (
                                      <EnhancedMediaSlider
                                        media={mediaItems}
                                        alt={activity.title || t('careTeam.defaultMediaAlt')}
                                        interval={3000}
                                      />
                                    ) : (
                                      // Use file extension to check for video
                                      mediaItems[0].url.match(/\.(mp4|webm|ogg)$/i) ? (
                                        <video
                                          src={mediaItems[0].url}
                                          controls
                                          className="w-full h-full object-contain"
                                          playsInline
                                        />
                                      ) : (
                                        <Image
                                          src={mediaItems[0].url || '/logo.jpeg'}
                                          alt={activity.title || t('careTeam.defaultMediaAlt')}
                                          fill
                                          className="object-contain"
                                          sizes="(max-width: 768px) 100vw, 33vw"
                                        />
                                      )
                                    )}
                                  </div>
                                </TabsContent>
                              )}
                              {hasDrive && (
                                <TabsContent value="drive">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                    <GoogleDriveEmbed videoId={link} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                  </div>
                                </TabsContent>
                              )}
                              {hasYouTube && (
                                <TabsContent value="youtube">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                    <YouTubeEmbed videoId={link} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                  </div>
                                </TabsContent>
                              )}
                            </Tabs>
                          )}
                          {!(hasMedia || hasDrive || hasYouTube) && (
                            <div className="relative aspect-video">
                              <Image
                                src="/logo.jpeg"
                                alt={activity.title || t('careTeam.defaultMediaAlt')}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.title}</h3>
                            <div className="space-y-2 mb-4">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                <MapPin className="h-4 w-4" />
                                <span>{activity.location}</span>
                              </div>
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {content}
                            </div>

                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(activity.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    {t('careTeam.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('careTeam.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {t('careTeam.noActivities')}
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

// Main component with Suspense wrapper
export default function CareTeamPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "關愛隊",
            en: "Care Team",
          }}
          description={{
            zh: "了解九龍總商會關愛隊的社會公益活動和社區服務項目",
            en: "Learn about KCC Care Team's social welfare activities and community service projects",
          }}
          image="/logo.jpeg"
        />
        <div className="container py-12">
          <div className="grid md:grid-cols-2 gap-6">
            {Array(4).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    }>
      <CareTeamContent />
    </Suspense>
  )
}
