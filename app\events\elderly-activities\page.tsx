'use client'

import { useState, useEffect, use<PERSON>emo, Suspense } from 'react'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, Heart, ChevronDown, ChevronUp } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import AnimatedHero from '@/components/animated-hero'
import { fetchElderlyActivities, type ElderlyActivity } from '@/lib/strapi'
import ImageSlider from '@/components/image-slider'
import { useTranslation, getLanguageFromParams } from '@/lib/translations'
import { useSearchParams } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import FullImageSlider from '@/components/full-image-slider'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from '@/components/ui/tabs'

// Helper functions for link type detection
const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

// Mock data for elderly activities
const mockElderlyActivities = [
  {
    id: 1,
    title: "樂齡健康講座",
    titleEn: "Elderly Health Seminar",
    description: "邀請專業醫生為長者講解健康保健知識，包括飲食營養、運動保健等實用資訊。",
    descriptionEn: "Professional doctors share health knowledge for seniors, including nutrition, exercise and practical health information.",
    date: "2024-03-10",
    location: "九龍總商會多功能廳",
    locationEn: "KCC Multi-purpose Hall",
    participants: "150+ 長者會員",
    participantsEn: "150+ Senior Members",
    image: "/placeholder.svg"
  },
  {
    id: 2,
    title: "樂齡文化交流活動",
    titleEn: "Elderly Cultural Exchange Activity",
    description: "組織長者參觀博物館和文化景點，促進文化交流和社交互動。",
    descriptionEn: "Organizing seniors to visit museums and cultural sites, promoting cultural exchange and social interaction.",
    date: "2024-02-25",
    location: "香港歷史博物館",
    locationEn: "Hong Kong Museum of History",
    participants: "80+ 長者會員",
    participantsEn: "80+ Senior Members",
    image: "/placeholder.svg"
  }
]

// Component that uses useSearchParams - needs to be wrapped in Suspense
function ElderlyActivitiesContent() {
  const searchParams = useSearchParams()
  const [activities, setActivities] = useState<ElderlyActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})
  const [expandedActivity, setExpandedActivity] = useState<number | null>(null)

  // Get current language and translation function
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Map app language to Strapi locale
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'; // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK'; // Simplified Chinese Hong Kong
      case 'en':
        return 'en'; // English
      default:
        return undefined; // Use default locale
    }
  }, [currentLang]);

  // Memoize translation function to prevent re-renders
  const memoizedT = useMemo(() => t, [currentLang])

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 100;
  }

  // Helper function to get image URL from Strapi image object
  const getImageUrl = (image: any) => {
    if (image.formats) {
      if (image.formats.medium?.url) return image.formats.medium.url;
      if (image.formats.small?.url) return image.formats.small.url;
      if (image.formats.large?.url) return image.formats.large.url;
      if (image.formats.thumbnail?.url) return image.formats.thumbnail.url;
    }
    return image.url || "/placeholder.svg";
  }

  // Helper function to render rich text content
  const renderRichText = (content: string | Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>) => {
    try {
      if (typeof content === 'string') {
        return <p className="text-gray-600 mb-4">{content}</p>
      }

      if (!content || !Array.isArray(content)) {
        return <p className="text-gray-600 mb-4">No content available</p>
      }

      return content.map((block, index) => {
        if (block.type === "paragraph") {
          return (
            <p key={index} className="text-gray-600 mb-4">
              {block.children?.map((child: any, childIndex: number) => (
                <span key={childIndex}>{child.text || ''}</span>
              ))}
            </p>
          )
        }
        return null
      })
    } catch (error) {
      console.error('Error rendering rich text:', error)
      return <p className="text-gray-600 mb-4">Content unavailable</p>
    }
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        // Build API URL with locale
        let url = 'https://strapibackendproject-3x6s.onrender.com/api/elderly-activities?populate=*';
        url += '&pagination[pageSize]=5000';
        if (getStrapiLocale) {
          url += `&locale=${getStrapiLocale}`;
        }
        const headers: Record<string, string> = {
          'Content-Type': 'application/json',
        };
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN;
        if (strapiToken) {
          headers['Authorization'] = `Bearer ${strapiToken}`;
        }
        const response = await fetch(url, {
          headers,
        });

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const result = await response.json();
        if (result && result.data) {
          // Sort by date descending (most recent first), fallback to createdAt
          const sorted = [...result.data].sort((a, b) => {
            const dateA = new Date(a.date || a.createdAt || 0).getTime();
            const dateB = new Date(b.date || b.createdAt || 0).getTime();
            return dateB - dateA;
          });
          setActivities(sorted);
        } else {
          setError('No activities found.');
        }
      } catch (err) {
        console.error('Error fetching elderly activities:', err);
        setError('Failed to fetch activities.');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [getStrapiLocale]); // Refetch when locale changes

  if (error) {
        return (
      <div className="min-h-screen">
        <AnimatedHero
          title="elderlyActivities.title"
          description="elderlyActivities.description"
          image="/placeholder.svg"
          lang={currentLang}
        />
        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {memoizedT('elderlyActivities.error')}
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="elderlyActivities.title"
        description="elderlyActivities.description"
        image="/placeholder.svg"
        lang={currentLang}
      />
      <div className="container py-12">
        <div className="grid gap-8">
          {activities.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">{memoizedT('elderlyActivities.noActivities')}</p>
              <p className="text-sm text-gray-400">
                {currentLang === 'zh' 
                  ? '長者活動頁面正在建設中，敬請期待。'
                  : currentLang === 'cn'
                  ? '长者活动页面正在建设中，敬请期待。'
                  : 'Elderly activities page is under construction. Please check back later.'
                }
              </p>
            </div>
          ) : (
            <div className="grid gap-8">
              {activities.map((activity) => {
                // Determine media and video links
                const link = activity.link ?? '';
                const hasDrive = isGoogleDriveLink(link);
                const hasYouTube = isYouTubeLink(link);
                const hasMedia = Array.isArray(activity.image) && activity.image.length > 0;
                const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';

                return (
                  <Card key={activity.id} className="overflow-hidden">
                    <CardContent className="p-0">
                      <div className="grid md:grid-cols-2 gap-6">
                        <div className="relative">
                          <Tabs defaultValue={defaultTab} className="w-full">
                            <TabsList className="flex w-full mb-2">
                              {hasMedia && <TabsTrigger value="media" className="flex-1">{t('mediaTab')}</TabsTrigger>}
                              {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('driveTab')}</TabsTrigger>}
                              {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                            </TabsList>
                            {hasMedia && (
                              <TabsContent value="media">
                                <div className="relative w-full">
                                  <EnhancedMediaSlider
                                    media={activity.image}
                                    alt={activity.activitytitle || 'Media'}
                                  />
                                </div>
                              </TabsContent>
                            )}
                            {hasDrive && (
                              <TabsContent value="drive">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <GoogleDriveEmbed videoId={link} title={activity.activitytitle || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                            {hasYouTube && (
                              <TabsContent value="youtube">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <YouTubeEmbed videoId={link} title={activity.activitytitle || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                          </Tabs>
                        </div>
                        <div className="p-6">
                          <div className="flex items-center gap-2 mb-4">
                            <span className="text-sm text-gray-500">
                              {activity.date ? new Date(activity.date).toLocaleDateString(currentLang === 'zh' ? 'zh-HK' : 'en-HK') : ''}
                            </span>
                          </div>
                          {activity.location && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <MapPin className="h-4 w-4" />
                              <span>{activity.location}</span>
                            </div>
                          )}
                          <h3 className="text-xl font-semibold mb-2">{activity.activitytitle || ''}</h3>
                          {activity.description && renderRichText(activity.description)}
                          {activity.link && typeof activity.link === 'string' && !activity.link.includes('drive.google.com') && (
                            <a href={activity.link} target="_blank" rel="noopener noreferrer" className="block mt-2 text-blue-600 underline">
                              {memoizedT('elderlyActivities.driveLink') || 'Drive/External Link'}
                            </a>
                          )}
                          {/* Read More/Read Less button if description is long */}
                          {activity.description && shouldShowReadMore(activity.description) && (
                            <Button variant="outline" onClick={() => setExpandedActivity(expandedActivity === activity.id ? null : activity.id)}>
                              {expandedActivity === activity.id ? memoizedT('elderlyActivities.readLess') : memoizedT('elderlyActivities.readMore')}
                            </Button>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

// Loading component for Suspense fallback
function ElderlyActivitiesLoading() {
  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="elderlyActivities.title"
        description="elderlyActivities.description"
        image="/placeholder.svg"
        lang="en" // Default language for loading state
      />
      <div className="container py-4">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="prose prose-lg max-w-none text-center">
              <Skeleton className="h-8 w-64 mx-auto mb-4" />
              <Skeleton className="h-4 w-96 mx-auto" />
            </div>
          </CardContent>
        </Card>
      </div>
      <div className="container py-12">
        <div className="grid gap-8">
          {Array(3).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <CardContent className="p-0">
                <div className="grid md:grid-cols-2 gap-6">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <div className="p-6">
                    <Skeleton className="h-6 w-32 mb-4" />
                    <Skeleton className="h-4 w-24 mb-2" />
                    <Skeleton className="h-6 w-3/4 mb-2" />
                    <Skeleton className="h-16 w-full mb-4" />
                    <Skeleton className="h-8 w-24" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}

// Main export component with Suspense boundary
export default function ElderlyActivitiesPage() {
  return (
    <Suspense fallback={<ElderlyActivitiesLoading />}>
      <ElderlyActivitiesContent />
    </Suspense>
  )
}

