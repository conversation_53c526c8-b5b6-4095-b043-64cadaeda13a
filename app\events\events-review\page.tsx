'use client'

import { useState, useEffect, useMemo, Suspense, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchArchives, type ArchiveEvent } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import { useTranslation, getLanguageFromParams, type TranslationKey } from '@/lib/translations'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// Define media item interface for enhanced media slider
interface ArchiveMediaItem {
  id: number;
  formats: {
    thumbnail?: { url: string };
    small?: { url: string };
    medium?: { url: string };
    large?: { url: string };
  };
  url: string;
}

// Component that uses searchParams
function EventsReviewContent() {
  const [archives, setArchives] = useState<ArchiveEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  const toggleExpanded = useCallback((id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }, [])

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = useCallback((content: any, limit = 3) => {
    if (!content) return false;

    if (typeof content === 'string') {
      return content.length > 200; // Character limit for plain strings
    } else if (Array.isArray(content)) {
      // Count paragraph blocks for rich text content
      const paragraphBlocks = content.filter((block: any) => block.type === 'paragraph' || (block.children && block.children.some((child: any) => child.text)));
      return paragraphBlocks.length > limit; // Paragraph limit for rich text
    }
    return false;
  }, []);

  // Helper function to get archive media items compatible with EnhancedMediaSlider
  const getArchiveMedia = useCallback((archive: ArchiveEvent): ArchiveMediaItem[] => {
    const mediaItems: ArchiveMediaItem[] = [];
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

    if (archive.image && Array.isArray(archive.image)) {
      archive.image.forEach((img) => {
        if (img && img.url) {
          mediaItems.push({
            id: img.id,
            url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
            formats: img.formats
          });
        }
      });
    }

    return mediaItems;
  }, [])

  // Helper function to render description content
  const renderDescription = useCallback((description: any, isExpanded: boolean, limit = 3) => {
    if (!description) return null;
    
    if (typeof description === 'string') {
      // This case is already handled by the outer div using dangerouslySetInnerHTML
      return description;
    }

    // Handle rich text content (array of blocks)
    const paragraphBlocks = description.filter((block: any) => block.type === 'paragraph' || (block.children && block.children.some((child: any) => child.text)));
    const blocksToRender = isExpanded ? paragraphBlocks : paragraphBlocks.slice(0, limit);

    return (
      <div className="space-y-4">
        {blocksToRender.map((block: any, blockIndex: number) => {
            return (
              <p key={blockIndex} className="leading-relaxed">
                {block.children?.map((child: any, childIndex: number) => {
                  if (child.text) {
                    return (
                      <span 
                        key={childIndex}
                        dangerouslySetInnerHTML={{
                          __html: child.text.split('\n').map((line: string) => line.trim()).join('<br />')
                        }}
                      />
                    );
                  }
                  return null;
                })}
              </p>
            );
        })}
      </div>
    );
  }, []);

  // Helper function to determine if a URL is a Google Drive link
  const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
  const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
      

        const archivesData = await fetchArchives({
          populate: '*',
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        });

       

        if (isMounted) {
          if (archivesData && archivesData.length > 0) {
            // Sort by date descending
            const sortedArchives = archivesData.sort((a, b) => {
              if (!a.date && !b.date) return 0;
              if (!a.date) return 1;
              if (!b.date) return -1;
              return new Date(b.date).getTime() - new Date(a.date).getTime();
            });
            setArchives(sortedArchives);
            setError(null);
          } else {
            setArchives([]);
            setError(null);
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching archives:', err);
          setError('An error occurred while fetching archives data.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [currentLang, getStrapiLocale])

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: t('eventsReview.title'),
            en: t('eventsReview.title'),
          }}
          description={{
            zh: t('eventsReview.description'),
            en: t('eventsReview.description'),
          }}
          image="/logo.jpeg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {error}
            </h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: t('eventsReview.title'),
          en: t('eventsReview.title'),
        }}
        description={{
          zh: t('eventsReview.description'),
          en: t('eventsReview.description'),
        }}
        image="/logo.jpeg"
      />

      <div className="container py-12">
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {archives.length > 0 ? (
                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    {archives.map((archive) => {
                      const isExpanded = expandedCards[archive.id]
                      const mediaItems = getArchiveMedia(archive)
                      const link = archive.youtubelink ?? '';
                      const hasDrive = isGoogleDriveLink(link);
                      const hasYouTube = isYouTubeLink(link);
                      const hasMedia = mediaItems.length > 0;
                      const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';

                      return (
                        <Card key={archive.id} className="hover:shadow-lg transition-all">
                          <Tabs defaultValue={defaultTab} className="w-full">
                            <TabsList className="flex w-full mb-2">
                              {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab') || 'Media'}</TabsTrigger>}
                              {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab') || 'Drive'}</TabsTrigger>}
                              {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                            </TabsList>
                            {hasMedia && (
                              <TabsContent value="media">
                                {mediaItems.length > 1 ? (
                                  <div className="relative aspect-w-16 aspect-h-12 w-full">
                                    <EnhancedMediaSlider
                                      media={mediaItems}
                                      alt={archive.title || t('eventsReview.defaultImageAlt')}
                                      interval={3000}
                                    />
                                  </div>
                                ) : mediaItems.length === 1 ? (
                                  <div className="relative aspect-w-16 aspect-h-12 w-full">
                                    <Image
                                      src={mediaItems[0].url || '/logo.jpeg'}
                                      alt={archive.title || t('eventsReview.defaultImageAlt')}
                                      fill
                                      className="object-contain"
                                      sizes="(max-width: 768px) 100vw, 50vw"
                                    />
                                  </div>
                                ) : null}
                              </TabsContent>
                            )}
                            {hasDrive && (
                              <TabsContent value="drive">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <GoogleDriveEmbed videoId={link} title={archive.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                            {hasYouTube && (
                              <TabsContent value="youtube">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <YouTubeEmbed videoId={link} title={archive.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                          </Tabs>
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(archive.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{archive.title}</h3>

                            {/* Description with Read More/Less */}
                            {archive.description && (
                                <div className="mb-4">
                                    <div
                                        className={`text-sm text-muted-foreground ${typeof archive.description === 'string' ? 'whitespace-pre-wrap' : ''}`}
                                        dangerouslySetInnerHTML={typeof archive.description === 'string' ? { __html: archive.description.split('\n').map(line => line.trim()).join('<br />') } : undefined}
                                    >
                                        {typeof archive.description !== 'string' && renderDescription(archive.description, isExpanded)}
                                    </div>
                                </div>
                            )}

                            {archive.description && shouldShowReadMore(archive.description) && (
                              <Button
                                variant="ghost"
                                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                                onClick={() => toggleExpanded(archive.id)}
                              >
                                {isExpanded ? (
                                  <>
                                    {t('eventsReview.showLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('eventsReview.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}

                            <div className="space-y-2 mt-4">
                              {archive.location && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <MapPin className="h-4 w-4" />
                                  <span>{archive.location}</span>
                                </div>
                              )}
                              {archive.participants && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{archive.participants}</span>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {t('eventsReview.noEvents')}
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

// Main component with Suspense wrapper
export default function EventsReviewPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: "活動回顧",
            en: "Events Review",
          }}
          description={{
            zh: "查看九龍總商會過往活動的精彩回顧",
            en: "View highlights from past KCC events",
          }}
          image="/logo.jpeg"
        />
        <div className="container py-12">
          <div className="grid md:grid-cols-2 gap-6">
            {Array(4).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    }>
      <EventsReviewContent />
    </Suspense>
  )
}
