"use client"

import { useState, useEffect, useMemo, Suspense, useCallback } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import AnimatedHero from "@/components/animated-hero"
import { fetchExchangeVisits } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from "lucide-react"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { useSearchParams } from "next/navigation"
import EnhancedMediaSlider from "@/components/enhanced-media-slider"
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// Define interfaces for media handling
interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText?: string
  caption?: string
  width: number | null
  height: number | null
  formats?: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl?: string
  provider: string
  provider_metadata?: any
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale?: string
  isVideo?: boolean
}

interface ExchangeVisit {
  id: number
  documentId: string
  title: string
  date?: string
  location?: string
  participants?: string
  description?: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  image?: StrapiImage[]
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
}

// Main page component with Suspense
export default function ExchangeVisitsPage() {
  return (
    <Suspense fallback={<ExchangeVisitsLoading />}>
      <ExchangeVisitsContent />
    </Suspense>
  )
}

// Component that uses searchParams
function ExchangeVisitsContent() {
  const [exchangeVisits, setExchangeVisits] = useState<ExchangeVisit[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({})

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  const toggleExpanded = useCallback((key: string) => {
    setExpandedSections(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }, [])

  useEffect(() => {
    let isMounted = true

    const loadExchangeVisits = async () => {
      try {
        setLoading(true)
        setError(null)
       

        const response = await fetchExchangeVisits({
          populate: "*",
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        })

        if (!isMounted) return

        if (response && response.data) {
          const visits = Array.isArray(response.data) ? response.data : [response.data]
          
          if (visits.length > 0) {
            // Sort visits by date descending (most recent first), fallback to createdAt
            const sortedVisits = [...visits].sort((a, b) => {
              const dateA = new Date(a.date || a.createdAt || 0).getTime();
              const dateB = new Date(b.date || b.createdAt || 0).getTime();
              return dateB - dateA;
            })
            setExchangeVisits(sortedVisits)
          } else {
            // Fallback without locale
            try {
              const fallbackResponse = await fetchExchangeVisits({ populate: '*', pagination: { pageSize: 5000 } })
              
              if (!isMounted) return

              if (fallbackResponse && fallbackResponse.data) {
                const fallbackVisits = Array.isArray(fallbackResponse.data) ? fallbackResponse.data : [fallbackResponse.data]
                if (fallbackVisits.length > 0) {
                  // Sort visits by date descending (most recent first), fallback to createdAt
                  const sortedFallbackVisits = [...fallbackVisits].sort((a, b) => {
                    const dateA = new Date(a.date || a.createdAt || 0).getTime();
                    const dateB = new Date(b.date || b.createdAt || 0).getTime();
                    return dateB - dateA;
                  })
                  setExchangeVisits(sortedFallbackVisits)
                } else {
                  setError(t('exchangeVisits.noExchangeVisits'))
                }
              } else {
                setError(t('exchangeVisits.noExchangeVisits'))
              }
            } catch (fallbackErr) {
              if (isMounted) {
                setError(t('exchangeVisits.noExchangeVisits'))
              }
            }
          }
        } else {
          setError(t('exchangeVisits.noExchangeVisits'))
        }
      } catch (err) {
        if (isMounted) {
          console.error("Error loading exchange visits:", err)
          setError(t('exchangeVisits.error'))
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    loadExchangeVisits()

    return () => {
      isMounted = false
    }
  }, [currentLang, getStrapiLocale]) // Removed 't' from dependencies

  // Helper function to get media items compatible with EnhancedMediaSlider
  const getExchangeVisitMedia = useCallback((visit: ExchangeVisit) => {
    const mediaItems = []
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'

    if (visit.image && Array.isArray(visit.image)) {
      mediaItems.push(...visit.image.map(img => ({
        id: img.id,
        url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        mime: img.mime,
        previewUrl: img.previewUrl,
        alternativeText: img.alternativeText,
        formats: img.formats
      })))
    }

    return mediaItems
  }, [])

  // Helper function to render content with expand/collapse
  const renderContent = useCallback((content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined, key: string) => {
    if (!content) return null

    const isExpanded = expandedSections[key]
    const isLong = typeof content === 'string' ? content.length > 150 : 
      Array.isArray(content) && content.reduce((acc, block) => 
        acc + (block.children?.map(child => child.text).join('') || ''), '').length > 150

    return (
      <div className="relative">
        <div className={`text-sm text-muted-foreground ${!isExpanded && isLong ? 'line-clamp-3' : ''}`}>
          {typeof content === 'string' ? (
            content.split('\n').map((line, index) => (
              <p key={index} className={index > 0 ? 'mt-1' : ''}>{line}</p>
            ))
          ) : (
            content.map((block, blockIndex) => (
              <p key={blockIndex} className="mb-2">
                {block.children.map((child, childIndex) => (
                  <span key={childIndex}>{child.text}</span>
                ))}
              </p>
            ))
          )}
        </div>
        
        {isLong && (
          <Button
            variant="link"
            size="sm"
            onClick={() => toggleExpanded(key)}
            className="mt-2 p-0 h-auto text-primary hover:text-primary/80 inline-flex items-center"
          >
            {isExpanded ? (
              <>
                {t('exchangeVisits.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
              </>
            ) : (
              <>
                {t('exchangeVisits.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
              </>
            )}
          </Button>
        )}
      </div>
    )
  }, [expandedSections, toggleExpanded, t])

  // Add these helpers after imports:
  const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
  const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            en: t('exchangeVisits.title'),
            zh: t('exchangeVisits.title'),
            cn: t('exchangeVisits.title')
          }}
          description={{
            en: t('exchangeVisits.description'),
            zh: t('exchangeVisits.description'),
            cn: t('exchangeVisits.description')
          }}
          image="/logo.jpeg"
          lang={currentLang}
        />
        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {t('common.error')}
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          en: t('exchangeVisits.title'),
          zh: t('exchangeVisits.title'),
          cn: t('exchangeVisits.title')
        }}
        description={{
          en: t('exchangeVisits.description'),
          zh: t('exchangeVisits.description'),
          cn: t('exchangeVisits.description')
        }}
        image="/logo.jpeg"
        lang={currentLang}
      />

      <div className="container py-12">
        {loading ? (
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {Array(6).fill(null).map((_, index) => (
              <Card key={`skeleton-${index}`} className="overflow-hidden">
                <div className="relative aspect-video">
                  <Skeleton className="absolute inset-0" />
                </div>
                <CardContent className="p-6 space-y-4">
                  <Skeleton className="h-4 w-32" />
                  <Skeleton className="h-6 w-3/4" />
                  <Skeleton className="h-16 w-full" />
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="space-y-6">
            {exchangeVisits.length > 0 ? (
              <>
                

                <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 items-start">
                  {exchangeVisits.map((visit) => {
                    const mediaItems = getExchangeVisitMedia(visit)
                    const driveLink = (visit as any).drivelink ?? '';
                    const hasDrive = isGoogleDriveLink(driveLink);
                    const hasYouTube = isYouTubeLink(driveLink);
                    const hasMedia = mediaItems.length > 0;
                    const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';

                    return (
                      <Card key={visit.id} className="overflow-hidden hover:shadow-lg transition-all flex flex-col">
                        {(hasMedia || hasDrive || hasYouTube) ? (
                          <Tabs defaultValue={defaultTab} className="w-full">
                            <TabsList className="flex w-full mb-2">
                              {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                              {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                              {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                            </TabsList>
                            {hasMedia && (
                              <TabsContent value="media">
                                {mediaItems.length > 1 ? (
                                  <EnhancedMediaSlider
                                    media={mediaItems}
                                    alt={visit.title || t('exchangeVisits.defaultMediaAlt')}
                                    interval={3000}
                                  />
                                ) : (
                                  <div className="relative aspect-video w-full min-h-[1px] flex items-center justify-center bg-gray-50 overflow-hidden">
                                    {mediaItems[0].mime?.startsWith('video/') ? (
                                      <video
                                        src={mediaItems[0].url}
                                        poster={mediaItems[0].previewUrl || undefined}
                                        controls
                                        className="w-full h-full object-contain"
                                        playsInline
                                      />
                                    ) : (
                                      <Image
                                        src={mediaItems[0].url || '/logo.jpeg'}
                                        alt={visit.title || t('exchangeVisits.defaultMediaAlt')}
                                        fill
                                        className="object-contain"
                                        sizes="(max-width: 768px) 100vw, 33vw"
                                      />
                                    )}
                                  </div>
                                )}
                              </TabsContent>
                            )}
                            {hasDrive && (
                              <TabsContent value="drive">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <GoogleDriveEmbed videoId={driveLink} title={visit.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                            {hasYouTube && (
                              <TabsContent value="youtube">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <YouTubeEmbed videoId={driveLink} title={visit.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                          </Tabs>
                        ) : (
                          <div className="flex items-center justify-center aspect-video bg-gray-100">
                            <p className="text-muted-foreground p-4 text-center">{t('exchangeVisits.noMedia')}</p>
                          </div>
                        )}

                        <CardContent className="p-6 flex flex-col flex-grow">
                          <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                            {visit.date && (
                              <>
                                <Calendar className="h-4 w-4" />
                                <time>
                                  {new Date(visit.date).toLocaleDateString(
                                    currentLang === 'zh' ? 'zh-HK' : 
                                    currentLang === 'cn' ? 'zh-CN' : 
                                    'en-US',
                                    { year: 'numeric', month: 'long', day: 'numeric' }
                                  )}
                                </time>
                              </>
                            )}
                            {visit.date && visit.location && " | "}
                            {visit.location && (
                              <>
                                <MapPin className="h-4 w-4" />
                                <span>{visit.location}</span>
                              </>
                            )}
                          </div>

                          <h3 className="text-xl font-bold mb-2">{visit.title}</h3>

                          {visit.participants && (
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Users className="h-4 w-4" />
                              <span>{visit.participants}</span>
                            </div>
                          )}

                          {visit.description && (
                            <div className="space-y-1">
                              {renderContent(visit.description, `description-${visit.id}`)}
                            </div>
                          )}
                        </CardContent>
                      </Card>
                    )
                  })}
                </div>
              </>
            ) : (
              <Card>
                <CardContent className="p-8">
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">{t('exchangeVisits.noExchangeVisits')}</p>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        )}
      </div>
    </div>
  )
}

// Loading component
function ExchangeVisitsLoading() {
  return (
    <div className="min-h-screen">
      <div className="w-full aspect-video bg-gray-200 animate-pulse"></div>
      <div className="container py-12">
        <Card className="mb-8">
          <CardContent className="p-8">
            <div className="space-y-4 text-center">
              <Skeleton className="h-8 w-3/4 mx-auto" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardContent>
        </Card>

        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {Array(6).fill(null).map((_, index) => (
            <Card key={`skeleton-${index}`} className="overflow-hidden">
              <div className="relative aspect-video">
                <Skeleton className="absolute inset-0" />
              </div>
              <CardContent className="p-6 space-y-4">
                <Skeleton className="h-4 w-32" />
                <Skeleton className="h-6 w-3/4" />
                <Skeleton className="h-16 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}