"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"

import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronDown, ChevronUp, Calendar, Star, ChevronLeft, ChevronRight } from "lucide-react"
import Image from "next/image"
import { <PERSON><PERSON>, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'

import AnimatedHero from "@/components/animated-hero"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { Skeleton } from "@/components/ui/skeleton"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId: string
  name: string
  alternativeText?: string
  caption?: string
  width: number | null
  height: number | null
  formats?: StrapiImageFormats
  hash: string
  ext: string
  mime: string
  size: number
  url: string
  previewUrl?: string
  provider: string
  provider_metadata?: any
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale?: string
  isVideo?: boolean
}

interface NewEraActivity {
  id: number
  documentId: string
  title: string
  date: string
  description: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string
  createdAt: string
  updatedAt: string
  publishedAt: string
  locale: string
  image?: StrapiImage | StrapiImage[] | null
  images?: StrapiImage[]
  seo?: any
  localizations?: any[]
  link?: string
}

// Custom ImageSlider component that shows full images and videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: StrapiImage[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Loading component
function NewEraLoading() {
  return (
    <div className="container py-12">
      <Card className="mb-8">
        <CardContent className="p-8">
          <div className="space-y-4">
            <Skeleton className="h-8 w-3/4 mx-auto" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
          </div>
        </CardContent>
      </Card>

      <div className="space-y-8">
        {Array(3).fill(0).map((_, index) => (
          <Card key={index}>
            <CardContent className="p-8">
              <div className="grid md:grid-cols-2 gap-8 items-start">
                <div className="space-y-4">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-10 w-3/4" />
                  <Skeleton className="h-20 w-full" />
                  <Skeleton className="h-20 w-full" />
                </div>
                <Skeleton className="h-64 w-full" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Main content component
function NewEraContent() {
  const searchParams = useSearchParams()
  const [activities, setActivities] = useState<NewEraActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedActivities, setExpandedActivities] = useState<Set<number>>(new Set())

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale - memoize to prevent re-renders
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese Hong Kong
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese Hong Kong
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }, [currentLang])

  const toggleExpanded = (activityId: number) => {
    setExpandedActivities(prev => {
      const newSet = new Set(prev)
      if (newSet.has(activityId)) {
        newSet.delete(activityId)
      } else {
        newSet.add(activityId)
      }
      return newSet
    })
  }

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true)
        const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'
        const strapiToken = process.env.NEXT_PUBLIC_STRAPI_TOKEN || '5848ecac1d83e40892d8c0f691aae9291f27ba65e861dc85209a9faa753df1032c44d66df909aeea39dc493442abe47e8e612b563c1487be9915499269d582ae8a9ab4bf75facb95c00ba38432e01cf568eaa39dcb3241d853d97f45394ceb4ab2dd9ca801df6cfda285e6bffb65e049c045a02cb0aee7bca76af467aa8b93ae'

        // Build URL with locale parameter
        const url = new URL(`${strapiUrl}/new-members`)
        url.searchParams.append('populate', '*')
        url.searchParams.append('pagination[pageSize]', '5000')
        if (getStrapiLocale) {
          url.searchParams.append('locale', getStrapiLocale)
        }

        const response = await fetch(url.toString(), {
          headers: {
            'Authorization': `Bearer ${strapiToken}`,
            'Content-Type': 'application/json',
          },
        })

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`)
        }

        const result = await response.json()
        

        if (result && result.data) {
          const activitiesData = result.data as NewEraActivity[]

          // Sort the activities by date descending (most recent first), fallback to createdAt
          const sortedActivities = [...activitiesData].sort((a, b) => {
            const dateA = new Date(a.date || a.createdAt || 0).getTime();
            const dateB = new Date(b.date || b.createdAt || 0).getTime();
            return dateB - dateA;
          })

          setActivities(sortedActivities)
        } else {
          console.error('New Era activities data not found in response:', result)
          setError(t('newEra.noActivities'))
        }
      } catch (error: any) {
        console.error('Error fetching new era activities data:', error)
        setError(t('newEra.error'))
      } finally {
        setLoading(false)
      }
    }

    fetchData()
  }, [currentLang, getStrapiLocale]) // Re-run when language changes

  // Helper function to render rich text content
  const renderRichText = (content: any[] | string) => {
    if (typeof content === 'string') {
      return <p className="mb-4">{content}</p>
    }

    if (!content || !Array.isArray(content)) {
      return <p className="text-muted-foreground">{t('newEra.noContent')}</p>
    }

    return content.map((block, index) => {
      if (block.type === "paragraph") {
        return (
          <p key={index} className="mb-4">
            {block.children?.map((child: any, childIndex: number) => (
              <span key={childIndex}>{child.text}</span>
            ))}
          </p>
        )
      }
      return null
    })
  }

  // Helper function to get activity images and videos
  const getActivityImages = (activity: NewEraActivity): StrapiImage[] => {
    const media: StrapiImage[] = []
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'

    // Check if image is an array (multiple media items)
    if (Array.isArray(activity.image)) {
      media.push(...activity.image.map(img => ({
        ...img,
        url: img.url?.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        isVideo: img.mime?.startsWith('video/') || false
      })))
    } else if (activity.image) {
      // Single media item
      media.push({
        ...activity.image,
        url: activity.image.url?.startsWith('http') ? activity.image.url : `${strapiUrl}${activity.image.url}`,
        isVideo: activity.image.mime?.startsWith('video/') || false
      })
    }

    // Also check images field
    if (activity.images && Array.isArray(activity.images)) {
      media.push(...activity.images.map(img => ({
        ...img,
        url: img.url?.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
        isVideo: img.mime?.startsWith('video/') || false
      })))
    }

    return media
  }

  // Helper function to get media URL with fallback
  const getImageUrl = (media: StrapiImage | undefined): string => {
    if (!media) return "/logo.jpeg"

    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com/api'

    // For videos, return the preview URL if available
    if (media.mime?.startsWith('video/')) {
      return media.previewUrl || "/logo.jpeg"
    }

    // For images, try different format sizes
    if (media.formats?.medium?.url) {
      return media.formats.medium.url.startsWith('http')
        ? media.formats.medium.url
        : `${strapiUrl}${media.formats.medium.url}`
    }
    if (media.formats?.small?.url) {
      return media.formats.small.url.startsWith('http')
        ? media.formats.small.url
        : `${strapiUrl}${media.formats.small.url}`
    }
    if (media.url) {
      return media.url.startsWith('http')
        ? media.url
        : `${strapiUrl}${media.url}`
    }

    return "/logo.jpeg"
  }

  const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
  const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

  return (
    <div className="container py-12">
      <div className="space-y-8">
        {loading ? (
          // Loading skeletons
          Array(3).fill(0).map((_, index) => (
            <Card key={index}>
              <CardContent className="p-8">
                <div className="grid md:grid-cols-2 gap-8 items-start">
                  <div className="space-y-4">
                    <Skeleton className="h-8 w-24" />
                    <Skeleton className="h-10 w-3/4" />
                    <Skeleton className="h-20 w-full" />
                    <Skeleton className="h-20 w-full" />
                  </div>
                  <Skeleton className="h-64 w-full" />
                </div>
              </CardContent>
            </Card>
          ))
        ) : error ? (
          <Card>
            <CardContent className="p-8">
              <div className="text-red-500 p-4 rounded-md bg-red-50">
                <p>{error}</p>
              </div>
            </CardContent>
          </Card>
        ) : activities.length > 0 ? (
          activities.map((activity) => {
            const isExpanded = expandedActivities.has(activity.id)
            const images = getActivityImages(activity)
            const link = activity.link ?? '';
            const hasDrive = isGoogleDriveLink(link);
            const hasYouTube = isYouTubeLink(link);
            const hasMedia = images.length > 0;
            const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';

            // Determine if content is long enough to show read more/less
            const hasLongContent = (typeof activity.description === 'string' && activity.description.length > 200) ||
                                 (Array.isArray(activity.description) && activity.description.length > 2)

            return (
              <Card key={activity.id} className="overflow-hidden">
                <CardContent className="p-8">
                  <div className="grid md:grid-cols-5 gap-8 items-start">
                    <div className="md:col-span-2 space-y-4">
                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                        <Calendar className="h-4 w-4" />
                        <time>{new Date(activity.date).toLocaleDateString()}</time>
                      </div>
                      <h1 className="text-3xl font-bold mb-4">{activity.title}</h1>

                      <div className="flex items-center gap-2 text-sm text-muted-foreground mb-4">
                        <Star className="h-4 w-4" />
                        <span>{t('newEra.newMemberEvent')}</span>
                      </div>

                      {/* Description */}
                      {activity.description && (
                        <div className={`space-y-4 ${!isExpanded && hasLongContent ? 'line-clamp-3' : ''}`}>
                          {renderRichText(activity.description)}
                        </div>
                      )}

                      {/* Read More/Less Button */}
                      {hasLongContent && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleExpanded(activity.id)}
                          className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                        >
                          {isExpanded ? (
                            <>
                              {t('newEra.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                            </>
                          ) : (
                            <>
                              {t('newEra.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                            </>
                          )}
                        </Button>
                      )}
                    </div>

                    {/* Tabs for Media/Drive/YouTube */}
                    <div className="md:col-span-3 relative w-full rounded-lg overflow-hidden bg-gray-50">
                      <Tabs defaultValue={defaultTab} className="w-full">
                        <TabsList className="flex w-full mb-2">
                          {hasMedia && <TabsTrigger value="media" className="flex-1">{t('mediaTab')}</TabsTrigger>}
                          {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('driveTab')}</TabsTrigger>}
                          {hasYouTube && <TabsTrigger value="youtube" className="flex-1">{t('youtubeTab')}</TabsTrigger>}
                        </TabsList>
                        {hasMedia && (
                          <TabsContent value="media">
                            {images.length > 1 ? (
                              <FullImageSlider
                                images={images}
                                alt={activity.title || 'New Era Activity'}
                                interval={3000}
                              />
                            ) : (
                              <div className="relative w-full aspect-[4/3]">
                                {images[0].mime?.startsWith('video/') ? (
                                  <video
                                    src={images[0].url}
                                    poster={images[0].previewUrl}
                                    controls
                                    className="w-full h-full object-contain"
                                    playsInline
                                  />
                                ) : (
                                  <Image
                                    src={getImageUrl(images[0])}
                                    alt={`${activity.title} - New Era Activity`}
                                    fill
                                    className="object-contain"
                                    sizes="(max-width: 768px) 100vw, 50vw"
                                  />
                                )}
                              </div>
                            )}
                          </TabsContent>
                        )}
                        {hasDrive && (
                          <TabsContent value="drive">
                            <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                              <GoogleDriveEmbed videoId={link} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                            </div>
                          </TabsContent>
                        )}
                        {hasYouTube && (
                          <TabsContent value="youtube">
                            <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                              <YouTubeEmbed videoId={link} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                            </div>
                          </TabsContent>
                        )}
                      </Tabs>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })
        ) : (
          <Card>
            <CardContent className="p-8">
              <div className="text-center py-12">
                <p className="text-muted-foreground">{t('newEra.noActivities')}</p>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}

// Component that uses searchParams - wrapped in Suspense
function NewEraPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="newEra.title"
        description="newEra.description"
        image="/logo.jpeg"
        lang={currentLang}
      />

      <Suspense fallback={<NewEraLoading />}>
        <NewEraContent />
      </Suspense>
    </div>
  )
}

// Main page component
export default function NewEraPage() {
  return (
    <Suspense fallback={<NewEraLoading />}>
      <NewEraPageContent />
    </Suspense>
  )
}