"use client"

import { useState, useEffect, useMemo, Suspense, useCallback } from "react"
import { useSearchParams } from 'next/navigation'
import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Calendar, ChevronDown, ChevronUp, MapPin, Users } from "lucide-react"
import { fetchReceptionGuests, type ReceptionGuest } from "@/lib/strapi"
import AnimatedHero from "@/components/animated-hero"
import EnhancedMediaSlider from "@/components/enhanced-media-slider"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import { Tabs, TabsList, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// Loading component
function ReceptionGuestsLoading() {
  return (
    <div className="container py-12">
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {Array(6).fill(null).map((_, index) => (
          <Card key={`skeleton-${index}`} className="overflow-hidden">
            <div className="relative aspect-video">
              <Skeleton className="absolute inset-0" />
            </div>
            <CardContent className="p-6 space-y-4">
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-16 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

// Main page component
export default function ReceptionGuestsPage() {
  return (
    <Suspense fallback={<ReceptionGuestsLoading />}>
      <ReceptionGuestsPageContent />
    </Suspense>
  )
}

// Single consolidated component
function ReceptionGuestsPageContent() {
  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // All state management in one place
  const [receptionGuests, setReceptionGuests] = useState<ReceptionGuest[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<Record<number, boolean>>({})

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return undefined
    }
  }, [currentLang])

  const toggleExpanded = useCallback((id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }, [])

  useEffect(() => {
    let isMounted = true

    const fetchData = async () => {
      try {
        setLoading(true)
        

        const guestsData = await fetchReceptionGuests({
          populate: '*',
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        })

        if (!isMounted) return

      

        if (guestsData && guestsData.length > 0) {
 

          // Remove duplicates based on documentId
          let uniqueGuests = guestsData.filter((guest, index, self) =>
            index === self.findIndex(g => g.documentId === guest.documentId)
          )

          // Sort by date descending (most recent first), fallback to createdAt
          uniqueGuests = uniqueGuests.sort((a, b) => {
            const dateA = new Date(a.date || a.createdAt || 0).getTime();
            const dateB = new Date(b.date || b.createdAt || 0).getTime();
            return dateB - dateA;
          });

          
          setReceptionGuests(uniqueGuests)
        } else {
          // Fallback without locale
          try {
            const fallbackData = await fetchReceptionGuests({ populate: '*', pagination: { pageSize: 5000 } })
            
            if (!isMounted) return

            if (fallbackData && fallbackData.length > 0) {
              let uniqueFallbackGuests = fallbackData.filter((guest, index, self) =>
                index === self.findIndex(g => g.documentId === guest.documentId)
              )
              // Sort by date descending, fallback to createdAt
              uniqueFallbackGuests = uniqueFallbackGuests.sort((a, b) => {
                const dateA = new Date(a.date || a.createdAt || 0).getTime();
                const dateB = new Date(b.date || b.createdAt || 0).getTime();
                return dateB - dateA;
              });
              setReceptionGuests(uniqueFallbackGuests)
            } else {
              setError(t('reception.noReceptionGuests'))
            }
          } catch (fallbackErr) {
            if (isMounted) {
              setError(t('reception.noReceptionGuests'))
            }
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching reception guests:', err)
          setError(t('reception.error'))
        }
      } finally {
        if (isMounted) {
          setLoading(false)
        }
      }
    }

    fetchData()

    return () => {
      isMounted = false
    }
  }, [currentLang, getStrapiLocale]) // Removed 't' from dependencies

  // Helper function to render structured content
  const renderStructuredContent = useCallback((content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined, isExpanded: boolean = false) => {
    if (!content) return null;

    if (typeof content === 'string') {
      return <div className="whitespace-pre-line">{content}</div>;
    }

    if (Array.isArray(content)) {
      const renderedContent = content.map((block, blockIndex) => {
        if (block.type === 'paragraph') {
          const text = block.children.map(child => child.text).join('');
          if (text.trim() === '') return null;

          return (
            <p key={blockIndex} className="mb-2">
              {block.children.map((child, childIndex) => (
                <span key={childIndex}>{child.text}</span>
              ))}
            </p>
          );
        }
        return null;
      }).filter(Boolean);

      return <div className={isExpanded ? '' : 'line-clamp-3'}>{renderedContent}</div>;
    }

    return null;
  }, []);

  const shouldShowReadMore = useCallback((content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | string | undefined) => {
    if (!content) return false;

    if (typeof content === 'string') {
      return content.length > 150;
    }

    if (Array.isArray(content)) {
      const totalText = content
        .map(block => block.children?.map(child => child.text).join('') || '')
        .join(' ');
      return totalText.length > 150;
    }

    return false;
  }, []);

  const getAllMedia = useCallback((guest: ReceptionGuest): Array<{
    id: number;
    url: string;
    mime?: string;
    previewUrl?: string | null;
    alternativeText?: string | null;
  }> => {
    let mediaItems: any[] = [];

    if (guest.images && guest.images.length > 0) {
      mediaItems = guest.images;
    } else if (guest.image) {
      if (Array.isArray(guest.image)) {
        mediaItems = guest.image;
      } else {
        mediaItems = [guest.image];
      }
    }

    const uniqueMedia = mediaItems.filter((item, index, self) =>
      index === self.findIndex(t => t.id === item.id)
    );

    return uniqueMedia;
  }, []);

  // Helper function to check if a URL is a Google Drive link
  const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
  const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

  if (error) {
    return (
      <div className="container py-12">
        <Card>
          <CardContent className="p-8">
            <div className="text-center py-12">
              <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
                <h2 className="text-xl font-semibold text-red-700 mb-2">
                  {t('common.error')}
                </h2>
                <p className="text-red-600">{error}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="reception.title"
        description="reception.description"
        image="/logo.jpeg"
        lang={currentLang}
      />

      <div className="container py-12">
        <section>
          {loading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {Array(6).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {receptionGuests.length > 0 ? (
                <div className="space-y-6">
                  <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                    {receptionGuests.map((guest) => {
                      const isExpanded = expandedCards[guest.id]
                      const showReadMore = shouldShowReadMore(guest.description)
                      const mediaItems = getAllMedia(guest)
                      const driveLink = guest.drivelink ?? '';
                      const hasDrive = isGoogleDriveLink(driveLink);
                      const hasYouTube = isYouTubeLink(driveLink);
                      const hasMedia = mediaItems.length > 0;
                      const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';

                      return (
                        <Card key={`reception-guest-${guest.id}`} className="overflow-hidden hover:shadow-lg transition-all">
                          <Tabs defaultValue={defaultTab} className="w-full">
                            <TabsList className="flex w-full mb-2">
                              {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                              {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                              {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                            </TabsList>
                            {hasMedia && (
                              <TabsContent value="media">
                                {mediaItems.length > 1 ? (
                                  <div className="relative aspect-w-16 aspect-h-12 w-full">
                                    <EnhancedMediaSlider
                                      media={mediaItems}
                                      alt={guest.title || "Reception Guest"}
                                      interval={3000}
                                    />
                                  </div>
                                ) : mediaItems.length === 1 ? (
                                  <div className="relative aspect-w-16 aspect-h-12 w-full">
                                    {mediaItems[0].mime?.startsWith('video/') ? (
                                      <video
                                        src={mediaItems[0].url}
                                        poster={mediaItems[0].previewUrl || undefined}
                                        controls
                                        className="w-full h-full object-contain"
                                        playsInline
                                      />
                                    ) : (
                                      <Image
                                        src={mediaItems[0].url || '/logo.jpeg'}
                                        alt={guest.title || "Reception Guest"}
                                        fill
                                        className="object-contain"
                                        sizes="(max-width: 768px) 100vw, 33vw"
                                      />
                                    )}
                                  </div>
                                ) : null}
                              </TabsContent>
                            )}
                            {hasDrive && (
                              <TabsContent value="drive">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <GoogleDriveEmbed videoId={driveLink} title={guest.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                            {hasYouTube && (
                              <TabsContent value="youtube">
                                <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                  <YouTubeEmbed videoId={driveLink} title={guest.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                </div>
                              </TabsContent>
                            )}
                          </Tabs>
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{guest.date}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{guest.title}</h3>
                            <div className="space-y-2 mb-4">
                              {guest.guests && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{guest.guests}</span>
                                </div>
                              )}
                              {guest.origin && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <MapPin className="h-4 w-4" />
                                  <span>{guest.origin}</span>
                                </div>
                              )}
                            </div>
                            <div className={`text-muted-foreground ${isExpanded ? '' : 'line-clamp-3'}`}>
                              {renderStructuredContent(guest.description, isExpanded)}
                            </div>
                            {showReadMore && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => toggleExpanded(guest.id)}
                                className="mt-2 p-0 h-auto text-primary hover:text-primary/80"
                              >
                                {isExpanded ? (
                                  <>
                                    {t('reception.readLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('reception.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <Card>
                  <CardContent className="p-8">
                    <div className="text-center py-12">
                      <p className="text-muted-foreground">{t('reception.noReceptionGuests')}</p>
                    </div>
                  </CardContent>
                </Card>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}