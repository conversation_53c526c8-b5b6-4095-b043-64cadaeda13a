"use client"

import { useState, useEffect, Suspense, useMemo } from "react"
import { useSearchParams } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { submitRegistration, fetchUserRegistrations } from "@/lib/strapi"
import type { Registration } from "@/lib/strapi"
import { toast } from "sonner"
import { useTranslation, getLanguageFromParams, TranslationKey } from "@/lib/translations"
import { useUser } from "@/contexts/user-context"

interface FormData {
  Name: string
  email: string
  Number: string
  eventDocumentId: string
  EventName: string
  OrganizationName: string
}

function RegisterForm() {
  // Force reload if coming from /events/upcoming
  useEffect(() => {
    if (typeof window !== 'undefined' && document.referrer.includes('/events/upcoming')) {
      window.location.reload();
    }
  }, []);

  const searchParams = useSearchParams()
  const { user } = useUser()
  const [isLoading, setIsLoading] = useState(false)
  const [formData, setFormData] = useState<FormData>({
    Name: "",
    email: searchParams.get("email") || user?.email || "",
    Number: "",
    eventDocumentId: searchParams.get("eventDocumentId") || "",
    EventName: searchParams.get("eventName") || "",
    OrganizationName: ""
  })
  const [formError, setFormError] = useState<string | null>(null);
  const [allRegistrations, setAllRegistrations] = useState<Registration[]>([]);

  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  // Update email field when user data becomes available
  useEffect(() => {
    if (user?.email && !formData.email) {
      setFormData(prev => ({
        ...prev,
        email: user.email
      }))
    }
  }, [user?.email, formData.email])

  useEffect(() => {
    if (!formData.eventDocumentId || formData.eventDocumentId.trim() === "") {
      setFormError("Invalid event. Please use a valid registration link or return to the events page.");
    }
  }, [formData.eventDocumentId]);

  // Fetch all registrations on mount
  useEffect(() => {
    const fetchAll = async () => {
      try {
        const regs = await fetchUserRegistrations("", undefined);
        setAllRegistrations(regs);
      } catch (err) {
        console.error("Error fetching all registrations:", err);
      }
    };
    fetchAll();
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setFormError(null);

    // Prevent submission if eventDocumentId is invalid
    const eventDocumentIdString = typeof formData.eventDocumentId === 'string'
      ? formData.eventDocumentId
      : String(formData.eventDocumentId) || '';

    if (!eventDocumentIdString || eventDocumentIdString.trim() === "") {
      setFormError("Invalid event. Please use a valid registration link or return to the events page.");
      setIsLoading(false);
      return;
    }

    try {
      // Check for existing registration (client-side, using latest data)
      const eventDocumentIdString = String(formData.eventDocumentId);
      let existingRegs = allRegistrations.filter(
        reg => reg.email && reg.email.toLowerCase() === formData.email.trim().toLowerCase() && (reg.eventDocumentId || "") === (eventDocumentIdString || "")
      );
      console.log('First check - Existing registrations for email+eventDocumentId:', existingRegs);
      if (existingRegs && existingRegs.length > 0) {
        setFormError(t('register.alreadyRegistered' as TranslationKey));
        setIsLoading(false);
        setTimeout(() => {
          const lang = currentLang || 'en';
          window.location.href = `/events/upcoming?lang=${lang}`;
        }, 3000);
        return;
      }

      // Final check: Verify no duplicate registration exists just before submission
      const finalCheck = await fetchUserRegistrations(formData.email.trim().toLowerCase(), eventDocumentIdString);
      if (finalCheck && finalCheck.length > 0) {
        console.log('Final check: Duplicate registration detected, aborting submission');
        setFormError("Registration failed: You have already registered for this event.");
        setIsLoading(false);
        return;
      }

      // Update formData with the correct string value
      const submissionData = {
        ...formData,
        eventDocumentId: eventDocumentIdString
      };

      const registrationResult = await submitRegistration(submissionData);
      console.log('Registration submission result:', registrationResult);

      toast.success(t('register.eventSuccessMessage' as TranslationKey));

      // Re-fetch all registrations after successful submission
      const regs = await fetchUserRegistrations("", undefined);
      setAllRegistrations(regs);

      setTimeout(() => {
        const lang = currentLang || 'en';
        window.location.href = `/events/upcoming?lang=${lang}`;
      }, 3000);
    } catch (error) {
      console.error("Error submitting registration:", error);
      toast.error(t('register.eventErrorMessage' as TranslationKey));
    } finally {
      setIsLoading(false);
    }
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setFormData(prev => ({
      ...prev,
      [id]: value
    }))
  }

  return (
    <Card className="w-full max-w-lg mx-auto">
      <CardContent className="p-6">
        <h2 className="text-2xl font-bold mb-6">{t('register.pageTitle')}</h2>
        <form onSubmit={handleSubmit} className="space-y-4">
          {formError && (
            <div className="text-red-600 font-semibold mb-2">{formError}</div>
          )}
          <div className="space-y-2">
            <label htmlFor="Name" className="font-medium">
              {t('register.nameLabel')} <span className="text-red-500">*</span>
            </label>
            <Input
              id="Name"
              placeholder={t('register.namePlaceholder')}
              required
              value={formData.Name}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="email" className="font-medium">
              {t('register.emailLabel')} <span className="text-red-500">*</span>
            </label>
            <Input
              id="email"
              type="email"
              placeholder={t('register.emailPlaceholder')}
              required
              value={formData.email}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="Number" className="font-medium">
              {t('register.phoneLabel')} <span className="text-red-500">*</span>
            </label>
            <Input
              id="Number"
              placeholder={t('register.phonePlaceholder')}
              required
              value={formData.Number}
              onChange={handleChange}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="EventName" className="font-medium">
              {t('register.eventNameLabel')} <span className="text-red-500">*</span>
            </label>
            <Input
              id="EventName"
              value={formData.EventName}
              readOnly
            />
          </div>
          <input
            type="hidden"
            id="eventDocumentId"
            value={formData.eventDocumentId}
            readOnly
          />

          <div className="space-y-2">
            <label htmlFor="OrganizationName" className="font-medium">
              {t('register.organizationLabel')} <span className="text-red-500">*</span>
            </label>
            <Input
              id="OrganizationName"
              placeholder={t('register.organizationPlaceholder')}
              required
              value={formData.OrganizationName}
              onChange={handleChange}
            />
          </div>

          <Button
            type="submit"
            className="w-full bg-[#1E1B4B] hover:bg-[#1E1B4B]/90"
            disabled={isLoading}
          >
            {isLoading ? t('register.submitting') : t('register.submitButton')}
          </Button>
        </form>
      </CardContent>
    </Card>
  )
}

function RegisterPageContent() {
  return (
    <div className="container py-12">
      <RegisterForm />
    </div>
  )
}

export default function RegisterPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <RegisterPageContent />
    </Suspense>
  )
}