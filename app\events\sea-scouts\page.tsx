// Path: app/events/sea-scouts/page.tsx
'use client'

import { useState, useEffect, useMemo, useCallback, Suspense, useRef } from 'react'
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchNavyActivities, type NavyActivity } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import { useTranslation, getLanguageFromParams, type TranslationKey } from '@/lib/translations'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger, TabsContent } from '@/components/ui/tabs'

interface StrapiMedia {
  id: number
  url: string
  mime?: string
  previewUrl?: string | null
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: {
    large?: { url: string }
    medium?: { url: string }
    small?: { url: string }
    thumbnail?: { url: string }
  }
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: StrapiMedia[]): StrapiMedia[] {
  return mediaArr.map((item: StrapiMedia) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

function SeaScoutsPageContent() {
  const [activities, setActivities] = useState<NavyActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const searchParams = useSearchParams()
  const searchParamsString = useMemo(() => searchParams.toString(), [searchParams])
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParamsString])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  // Memoize the translation function to prevent unnecessary re-renders
  const memoizedT = useCallback((key: TranslationKey) => t(key), [t])

  // Move fetchData outside useEffect and memoize it with stable dependencies
  const fetchData = useCallback(async () => {
    try {
      setLoading(true)
      const activitiesData = await fetchNavyActivities({
        populate: '*',
        locale: getStrapiLocale,
        pagination: { pageSize: 5000 }
      })

      if (activitiesData && activitiesData.length > 0) {
        // Sort activities by date descending (most recent first), fallback to createdAt
        const sortedActivities = [...activitiesData].sort((a, b) => {
          const dateA = new Date(a.date || a.createdAt || 0).getTime();
          const dateB = new Date(b.date || b.createdAt || 0).getTime();
          return dateB - dateA;
        });
        setActivities(sortedActivities)
        setError(null)
      } else {
        setActivities([])
        setError(memoizedT('seaScouts.noActivities'))
      }
    } catch (err: any) {
      console.error('Error fetching navy activities:', err)
      setError(memoizedT('common.error'))
    } finally {
      setLoading(false)
    }
  }, [getStrapiLocale, memoizedT])

  // Use a ref to track the previous locale
  const prevLocaleRef = useRef(getStrapiLocale)

  // Use a ref to prevent double-fetching in Strict Mode during development
  const initialized = useRef(false);

  // Update useEffect to only fetch when locale actually changes or on initial mount (once in Strict Mode)
  useEffect(() => {
    if (!initialized.current) {
      initialized.current = true;
      fetchData();
    } else if (prevLocaleRef.current !== getStrapiLocale) {
      // Only fetch if locale changes after initial mount
      prevLocaleRef.current = getStrapiLocale;
      fetchData();
    }
  }, [getStrapiLocale, fetchData]);

  const toggleExpanded = (id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = (content: string | undefined) => {
    return content && content.length > 200;
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title={{
          zh: t('seaScouts.title'),
          en: t('seaScouts.title'),
          cn: t('seaScouts.title'),
        }}
        description={{
          zh: t('seaScouts.description'),
          en: t('seaScouts.description'),
          cn: t('seaScouts.description'),
        }}
        image="/logo.jpeg"
      />

      <div className="container py-12">
        {/* Error Display */}
        {/* Only show error box for real technical errors, not for no data */}
        {/* Sea Scouts Activities Listing */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Sea Scouts Activities Items */}
              {activities.length > 0 ? (
                <div className="space-y-6">
                  <div className="grid md:grid-cols-2 gap-6">
                    {activities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const showReadMore = shouldShowReadMore(activity.description)
                      // Ensure activity.image is an array of StrapiMedia
                      const activityMedia: StrapiMedia[] = activity.image && Array.isArray(activity.image)
                        ? patchMediaMime(activity.image as StrapiMedia[])
                        : [];
                      // Use 'link' for Drive/YouTube, fallback to drivelink/youtubelink if needed
                      const link = (activity as any).link || (activity as any).drivelink || (activity as any).youtubelink || '';
                      const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
                      const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));
                      const hasDrive = isGoogleDriveLink(link);
                      const hasYouTube = isYouTubeLink(link);
                      const hasMedia = activityMedia.length > 0;
                      const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';
                      return (
                        <Card key={activity.id} className="overflow-hidden hover:shadow-lg transition-all">
                          {(hasMedia || hasDrive || hasYouTube) ? (
                            <Tabs defaultValue={defaultTab} className="w-full">
                              <TabsList className="flex w-full mb-2">
                                {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                                {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                                {hasYouTube && <TabsTrigger value="youtube" className="flex-1">{t('youtubeTab')}</TabsTrigger>}
                              </TabsList>
                              {hasMedia && (
                                <TabsContent value="media">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden">
                                    {activityMedia.length > 1 ? (
                                      <EnhancedMediaSlider
                                        media={activityMedia}
                                        alt={activity.title || t('seaScouts.defaultImageAlt')}
                                        interval={3000}
                                      />
                                    ) : (
                                      // Use file extension to check for video
                                      activityMedia[0].url.match(/\.(mp4|webm|ogg)$/i) ? (
                                        <video
                                          src={activityMedia[0].url}
                                          controls
                                          className="w-full h-full object-contain"
                                          playsInline
                                        />
                                      ) : (
                                        <Image
                                          src={activityMedia[0].url || '/logo.jpeg'}
                                          alt={activity.title || t('seaScouts.defaultImageAlt')}
                                          fill
                                          className="object-contain"
                                          sizes="(max-width: 768px) 100vw, 33vw"
                                        />
                                      )
                                    )}
                                  </div>
                                </TabsContent>
                              )}
                              {hasDrive && (
                                <TabsContent value="drive">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                    <GoogleDriveEmbed videoId={link} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                  </div>
                                </TabsContent>
                              )}
                              {hasYouTube && (
                                <TabsContent value="youtube">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                    <YouTubeEmbed videoId={link} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                  </div>
                                </TabsContent>
                              )}
                            </Tabs>
                          ) : (
                            <div className="relative aspect-w-16 aspect-h-12">
                              <Image
                                src="/logo.jpeg"
                                alt={activity.title || t('seaScouts.defaultImageAlt')}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.title}</h3>

                            {/* Description with Read More/Less */}
                            {activity.description && (
                              <div className="prose prose-sm max-w-none mb-4">
                                <div 
                                  className={`text-sm text-muted-foreground whitespace-pre-wrap ${!expandedCards[activity.id] ? 'line-clamp-3' : ''}`}
                                  dangerouslySetInnerHTML={{
                                    __html: activity.description
                                      .split('\n')
                                      .map(line => line.trim())
                                      .join('<br />')
                                  }}
                                />
                              </div>
                            )}

                            {activity.description && shouldShowReadMore(activity.description) && (
                              <Button
                                variant="ghost"
                                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                                onClick={() => toggleExpanded(activity.id)}
                              >
                                {expandedCards[activity.id] ? (
                                  <>
                                    {t('seaScouts.showLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('seaScouts.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}

                            <div className="space-y-2 mt-4">
                              {activity.location && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <MapPin className="h-4 w-4" />
                                  <span>{activity.location}</span>
                                </div>
                              )}
                              {activity.participants && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <Users className="h-4 w-4" />
                                  <span>{activity.participants}</span>
                                </div>
                              )}
                            </div>
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {t('seaScouts.noActivities')}
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

export default function SeaScoutsPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SeaScoutsPageContent />
    </Suspense>
  )
}