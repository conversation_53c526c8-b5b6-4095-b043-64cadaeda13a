'use client'

import { useState, useEffect, useMemo, Suspense, useCallback } from 'react'
import { useSearchParams } from 'next/navigation'
import Image from 'next/image'
import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { Calendar, MapPin, Users, ChevronDown, ChevronUp } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { fetchSocialCharityActivities, type SocialCharityActivity } from '@/lib/strapi'
import AnimatedHero from '@/components/animated-hero'
import EnhancedMediaSlider from '@/components/enhanced-media-slider'
import { useTranslation, getLanguageFromParams, type TranslationKey } from '@/lib/translations'
import YouTubeEmbed from '@/components/ui/youtube-embed'
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger, TabsContent } from '@/components/ui/tabs'

// Helper function to extract YouTube video ID from URL
const getYouTubeVideoId = (url: string): string | null => {
  const regExp = /^(?:https?:\/\/)?(?:www\.)?(?:m\.)?(?:youtube\.com|youtu\.be)\/(?:watch\?v=|embed\/|v\/|)([^\&\?]*).*/;
  const match = url.match(regExp);
  return (match && match[1].length === 11) ? match[1] : null;
};

// Define media item interface for enhanced media slider
interface SocialCharityMediaItem {
  id: number;
  formats?: {
    thumbnail?: { url: string };
    small?: { url: string };
    medium?: { url: string };
    large?: { url: string };
  };
  url: string;
}

// Component that uses searchParams
function SocialCharityContent() {
  const [socialCharityActivities, setSocialCharityActivities] = useState<SocialCharityActivity[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [expandedCards, setExpandedCards] = useState<{ [key: number]: boolean }>({})

  const searchParams = useSearchParams()
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return 'en'
    }
  }, [currentLang])

  const toggleExpanded = useCallback((id: number) => {
    setExpandedCards(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }, [])

  // Helper function to check if content is long enough for "Read More"
  const shouldShowReadMore = useCallback((content: any) => {
    if (!content) return false;
    // Handle both string and rich text content
    if (typeof content === 'string') {
      return content.length > 100;
    }
    // For rich text content (array of paragraphs)
    return content.some((block: any) => 
      block.children?.some((child: any) => child.text?.length > 100)
    );
  }, [])

  // Helper function to get social charity media items compatible with EnhancedMediaSlider
  const getSocialCharityMedia = useCallback((activity: SocialCharityActivity): SocialCharityMediaItem[] => {
    const mediaItems: SocialCharityMediaItem[] = [];
    const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

    if (activity.image && Array.isArray(activity.image)) {
      activity.image.forEach((img) => {
        if (img && img.url) {
          mediaItems.push({
            id: img.id,
            url: img.url.startsWith('http') ? img.url : `${strapiUrl}${img.url}`,
            formats: img.formats ? {
              thumbnail: img.formats.thumbnail ? { url: img.formats.thumbnail.url } : undefined,
              small: img.formats.small ? { url: img.formats.small.url } : undefined,
              medium: img.formats.medium ? { url: img.formats.medium.url } : undefined,
              large: img.formats.large ? { url: img.formats.large.url } : undefined,
            } : undefined
          });
        }
      });
    }

    return mediaItems;
  }, [])

  // Helper function to render description content
  const renderDescription = useCallback((description: any) => {
    if (!description) return null;
    
    if (typeof description === 'string') {
      return description;
    }

    // Handle rich text content
    return description.map((block: any, index: number) => (
      <p key={index}>
        {block.children?.map((child: any, childIndex: number) => (
          <span key={childIndex}>{child.text}</span>
        ))}
      </p>
    ));
  }, []);

  useEffect(() => {
    let isMounted = true;

    const fetchData = async () => {
      try {
        setLoading(true);
       

        const activitiesData = await fetchSocialCharityActivities({
          populate: '*',
          locale: getStrapiLocale,
          pagination: { pageSize: 5000 }
        });

       

        if (isMounted) {
          if (activitiesData && activitiesData.length > 0) {
            // Sort activities by date descending (most recent first), fallback to createdAt
            const sortedActivities = [...activitiesData].sort((a, b) => {
              const dateA = new Date(a.date || a.createdAt || 0).getTime();
              const dateB = new Date(b.date || b.createdAt || 0).getTime();
              return dateB - dateA;
            });
            setSocialCharityActivities(sortedActivities);
            setError(null);
          } else {
            setSocialCharityActivities([]);
            setError(null);
          }
        }
      } catch (err: any) {
        if (isMounted) {
          console.error('Error fetching social charity activities:', err);
          setError('An error occurred while fetching social charity activities data.');
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchData();

    return () => {
      isMounted = false;
    };
  }, [currentLang, getStrapiLocale])

  if (error) {
    return (
      <div className="min-h-screen">
        <AnimatedHero
          title={{
            zh: t('socialCharity.title'),
            en: t('socialCharity.title'),
            cn: t('socialCharity.title'),
          }}
          description={{
            zh: t('socialCharity.description'),
            en: t('socialCharity.description'),
            cn: t('socialCharity.description'),
          }}
          image="/logo.jpeg"
        />

        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              {error}
            </h2>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: t('socialCharity.title'),
          en: t('socialCharity.title'),
          cn: t('socialCharity.title'),
        }}
        description={{
          zh: t('socialCharity.description'),
          en: t('socialCharity.description'),
          cn: t('socialCharity.description'),
        }}
        image="/logo.jpeg"
      />

      <div className="container py-12">
        {/* Section introduction/info card removed as per user request */}
        <section>
          {loading ? (
            <div className="grid md:grid-cols-2 gap-6">
              {Array(4).fill(null).map((_, index) => (
                <Card key={`skeleton-${index}`} className="overflow-hidden">
                  <div className="relative aspect-video">
                    <Skeleton className="absolute inset-0" />
                  </div>
                  <CardContent className="p-6 space-y-4">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-16 w-full" />
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {socialCharityActivities.length > 0 ? (
                <div className="space-y-6">

                  <div className="grid md:grid-cols-2 gap-6">
                    {socialCharityActivities.map((activity) => {
                      const isExpanded = expandedCards[activity.id]
                      const showReadMore = shouldShowReadMore(activity.description)
                      const mediaItems = getSocialCharityMedia(activity)
                      const drivelink = (activity as any).drivelink || '';
                      const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
                      const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));
                      const hasDrive = isGoogleDriveLink(drivelink);
                      const hasYouTube = isYouTubeLink(drivelink);
                      const hasMedia = mediaItems.length > 0;
                      const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';



                      return (
                        <Card key={activity.id} className="hover:shadow-lg transition-all">
                          {(hasMedia || hasDrive || hasYouTube) ? (
                            <Tabs defaultValue={defaultTab} className="w-full">
                              <TabsList className="flex w-full mb-2">
                                {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                                {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                                {hasYouTube && <TabsTrigger value="youtube" className="flex-1">{t('youtubeTab')}</TabsTrigger>}
                              </TabsList>
                              {hasMedia && (
                                <TabsContent value="media">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden">
                                    {mediaItems.length > 1 ? (
                                      <EnhancedMediaSlider
                                        media={mediaItems}
                                        alt={activity.title || t('socialCharity.defaultImageAlt')}
                                        interval={3000}
                                      />
                                    ) : (
                                      // Use file extension to check for video
                                      mediaItems[0].url.match(/\.(mp4|webm|ogg)$/i) ? (
                                        <video
                                          src={mediaItems[0].url}
                                          controls
                                          className="w-full h-full object-contain"
                                          playsInline
                                        />
                                      ) : (
                                        <Image
                                          src={mediaItems[0].url || '/logo.jpeg'}
                                          alt={activity.title || t('socialCharity.defaultImageAlt')}
                                          fill
                                          className="object-contain"
                                          sizes="(max-width: 768px) 100vw, 33vw"
                                        />
                                      )
                                    )}
                                  </div>
                                </TabsContent>
                              )}
                              {hasDrive && (
                                <TabsContent value="drive">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                    <GoogleDriveEmbed videoId={drivelink} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                  </div>
                                </TabsContent>
                              )}
                              {hasYouTube && (
                                <TabsContent value="youtube">
                                  <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                    <YouTubeEmbed videoId={getYouTubeVideoId(drivelink) || ''} title={activity.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                  </div>
                                </TabsContent>
                              )}
                            </Tabs>
                          ) : (
                            <div className="relative aspect-video w-full">
                              <Image
                                src="/logo.jpeg"
                                alt={activity.title || t('socialCharity.defaultImageAlt')}
                                fill
                                className="object-cover"
                              />
                            </div>
                          )}
                          <CardContent className="p-6 flex flex-col flex-grow">
                            <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                              <Calendar className="h-4 w-4" />
                              <time>{new Date(activity.date).toLocaleDateString()}</time>
                            </div>
                            <h3 className="text-xl font-bold mb-2">{activity.title}</h3>
                            <div className="space-y-2 mb-4">
                              {activity.location && (
                                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                                  <MapPin className="h-4 w-4" />
                                  <span>{activity.location}</span>
                                </div>
                              )}
                            </div>
                            {activity.description && (
                              <div className="prose prose-sm max-w-none">
                                <div 
                                  className={`text-sm text-muted-foreground whitespace-pre-wrap ${!expandedCards[activity.id] ? 'line-clamp-3' : ''}`}
                                  dangerouslySetInnerHTML={{
                                    __html: activity.description
                                      .split('\n')
                                      .map(line => line.trim())
                                      .join('<br />')
                                  }}
                                />
                              </div>
                            )}

                            {activity.description && activity.description.length > 200 && (
                              <Button
                                variant="ghost"
                                className="mt-2 text-sm text-blue-600 hover:text-blue-800"
                                onClick={() => toggleExpanded(activity.id)}
                              >
                                {expandedCards[activity.id] ? (
                                  <>
                                    {t('socialCharity.showLess')} <ChevronUp className="ml-1 h-4 w-4" />
                                  </>
                                ) : (
                                  <>
                                    {t('socialCharity.readMore')} <ChevronDown className="ml-1 h-4 w-4" />
                                  </>
                                )}
                              </Button>
                            )}
                          </CardContent>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              ) : (
                <div className="text-center py-12">
                  <p className="text-muted-foreground">
                    {t('socialCharity.noActivities')}
                  </p>
                </div>
              )}
            </>
          )}
        </section>
      </div>
    </div>
  )
}

export default function SocialCharityPage() {
  return (
    <Suspense fallback={<div className="container py-12"><Skeleton className="h-96 w-full" /></div>}>
      <SocialCharityContent />
    </Suspense>
  )
} 