"use client";
export const dynamic = "force-dynamic";
import { Suspense } from "react";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTranslation, getLanguageFromParams } from "@/lib/translations";
import { toast } from "sonner";
import { fetchAPI } from "@/strapi";

function ForgotPasswordPageContent() {
  const [email, setEmail] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);
  const router = useRouter();

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    setIsLoading(true);
    try {
      await fetchAPI("auth/forgot-password", {}, {
        method: "POST",
        body: JSON.stringify({ email }),
      });
      setSubmitted(true);
      toast.success(t("forgotPassword.success"));
    } catch (error) {
      toast.error("Something went wrong. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-100 to-slate-300 px-2 sm:px-4 py-8">
      <div className="w-full max-w-md bg-white shadow-xl rounded-2xl p-4 sm:p-8 md:p-10 mx-auto">
        <h2 className="text-2xl sm:text-3xl font-bold mb-4 text-center">{t("forgotPassword.title")}</h2>
        {submitted ? (
          <p className="text-center text-green-600">{t("forgotPassword.success")}</p>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                {t("forgotPassword.email")}
              </label>
              <Input
                id="email"
                type="email"
                autoComplete="email"
                required
                value={email}
                onChange={e => setEmail(e.target.value)}
                placeholder={t("forgotPassword.emailPlaceholder")}
                className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition"
              />
            </div>
            <Button
              type="submit"
              className="w-full bg-[#1E1B4B] hover:bg-[#2d2870] text-white py-3 text-lg font-semibold rounded-lg shadow transition-all duration-150"
              disabled={isLoading}
            >
              {isLoading ? t("forgotPassword.sending") : t("forgotPassword.button")}
            </Button>
          </form>
        )}
      </div>
    </div>
  );
}

export default function ForgotPasswordPage() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ForgotPasswordPageContent />
    </Suspense>
  );
} 