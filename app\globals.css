@tailwind base;
@tailwind components;
@tailwind utilities;

/* Suppress Grammarly extension attributes that cause hydration mismatches */
[data-new-gr-c-s-check-loaded],
[data-gr-ext-installed] {
  /* These attributes are added by Grammarly extension and cause hydration mismatches */
  /* We suppress them to prevent hydration errors */
}

/* Additional styles to prevent hydration issues */
html {
  scroll-behavior: smooth;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Prevent layout shift during hydration */
* {
  box-sizing: border-box;
}

/* Ensure consistent rendering */
img {
  max-width: 100%;
  height: auto;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 244 47% 20%;
    --primary-foreground: 210 40% 98%;
    --secondary: 244 47% 30%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 244 47% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 244 47% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 244 47% 20%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.container {
  @apply px-4 mx-auto max-w-7xl;
}

