"use client"

import { useState, useEffect, Suspense, useMemo, useRef } from "react"
import { useSearchParams } from "next/navigation"
import { fetchListOfCouncilors, fetchPastCouncil41s, fetchPastCouncil40s, fetchPastCouncil39s, fetchPastCouncil38s, fetchPastCouncil37s } from "@/lib/strapi"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import Image from "next/image"
import { Skeleton } from "@/components/ui/skeleton"
import AnimatedHero from "@/components/animated-hero"
import ImageSlider from "@/components/image-slider"
import { getLanguageFromParams, useTranslation } from "@/lib/translations"
import { ChevronLeft, ChevronRight } from "lucide-react"
import Link from "next/link"
import React from "react"

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId?: string
  name?: string
  alternativeText?: string | null
  caption?: string | null
  width?: number
  height?: number
  formats?: StrapiImageFormats
  url?: string
}

interface CouncilorData {
  id: number
  documentId?: string
  year?: string
  position?: string
  name?: string
  biography?: any[]
  description?: any[]
  order?: number
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  locale?: string
  image?: StrapiImage | StrapiImage[]
  images?: StrapiImage[]
  seo?: any
}

interface StrapiResponse {
  data: Array<{
    id: number
    attributes?: CouncilorData
  } & CouncilorData>
  meta?: {
    pagination?: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Helper function to render media content (image or video)
function renderMediaContent(mediaList: any[], alt: string, isModal: boolean = false) {
  if (mediaList.length > 1) {
    return (
      <FullImageSlider
        images={mediaList}
        alt={alt}
        interval={isModal ? 4000 : 3000} // Different interval for modal
      />
    );
  } else if (mediaList.length === 1) {
    const mediaItem = mediaList[0];
    const isVideo = typeof mediaItem.mime === 'string' && mediaItem.mime.startsWith('video/');
    const imageSizes = isModal ? "(max-width: 768px) 100vw, 50vw" : "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw";

    return (
      <div className={`relative aspect-[4/3] w-full min-h-[400px] ${isModal ? 'aspect-square' : ''}`}>
        {isVideo ? (
          <video
            src={String(mediaItem.url) || '/logo.jpeg'}
            poster={String(mediaItem.previewUrl) || ''}
            controls
            className={`w-full h-full object-contain ${isModal ? 'rounded-lg' : ''}`}
            playsInline
          />
        ) : (
          <Image
            src={mediaItem.url ? String(mediaItem.url) : '/logo.jpeg'}
            alt={alt}
            fill
            className={`object-contain ${isModal ? 'rounded-lg' : ''}`}
            sizes={imageSizes}
          />
        )}
      </div>
    );
  } else {
    return (
      <div className={`aspect-[4/3] bg-muted flex items-center justify-center min-h-[400px] ${isModal ? 'aspect-square' : ''}`}>
        <span className="text-8xl">👤</span>
      </div>
    );
  }
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

const YEAR_TABS = [
  { label: "42nd", key: "42nd" },
  { label: "41st", key: "41st" },
  { label: "40th", key: "40th" },
  { label: "39th", key: "39th" },
  { label: "38th", key: "38th" },
  { label: "37th", key: "37th" },
];

function CouncilorsContent() {
  const searchParams = useSearchParams()
  const [councilorData, setCouncilorData] = useState<CouncilorData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedCouncilor, setSelectedCouncilor] = useState<CouncilorData | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const fetchInitiated = useRef(false) // Use ref to prevent double fetch in strict mode
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)
  const [activeYear, setActiveYear] = useState("42nd");

  // Map currentLang to Strapi locale
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK';
      case 'cn':
        return 'zh-Hans-HK';
      case 'en':
        return 'en';
      default:
        return 'en';
    }
  }, [currentLang]);


  // Find the 監事會 entry for special heading placement (for 42nd, 41st, 40th, 39th, 38th, 37th)
  const kanshikaiEntry = useMemo(() => {
    return (activeYear === "42nd" || activeYear === "41st" || activeYear === "40th" || activeYear === "39th" || activeYear === "38th" || activeYear === "37th")
      ? councilorData.find(c => c.name === "監事會")
      : null;
  }, [councilorData, activeYear]);

  useEffect(() => {
    const loadCouncilors = async () => {
      try {
        setLoading(true);
        setError(null);
        let response;
        
        
        if (activeYear === "42nd") {
         
          response = await fetchListOfCouncilors({ 
            locale: getStrapiLocale,
            populate: "*"
          });
        } else if (activeYear === "41st") {
        
          response = await fetchPastCouncil41s({ 
            locale: getStrapiLocale,
            populate: "*"
          });
        } else if (activeYear === "40th") {
         
          response = await fetchPastCouncil40s({ 
            locale: getStrapiLocale,
            populate: "*"
          });
        } else if (activeYear === "39th") {
         
          response = await fetchPastCouncil39s({ 
            locale: getStrapiLocale,
            populate: "*"
          });
        } else if (activeYear === "38th") {
          
          response = await fetchPastCouncil38s({ 
            locale: getStrapiLocale,
            populate: "*"
          });
        } else if (activeYear === "37th") {
          
          response = await fetchPastCouncil37s({ 
            locale: getStrapiLocale,
            populate: "*"
          });
        } else {
         
          response = { data: [] }; // Placeholder for other years
        }

        

        if (response && Array.isArray(response.data)) {
          const processedData = response.data.map((item: any) => {
            const processedItem = { ...item };
            if (item.attributes) {
              Object.assign(processedItem, item.attributes);
              processedItem.id = item.id;
            }
            return processedItem;
          }).sort((a: any, b: any) => a.id - b.id);
          
          
          setCouncilorData(processedData);
        } else {
          console.error('Unexpected response format:', response);
          setError("Failed to load council members due to unexpected data format.");
        }
      } catch (err) {
        console.error('Error loading councilors:', err);
        setError(err instanceof Error ? err.message : "An unknown error occurred");
      } finally {
        setLoading(false);
      }
    };

    loadCouncilors();
  }, [activeYear, getStrapiLocale]); // Only depend on activeYear and getStrapiLocale

  // Group councilors by section (based on order), but exclude order 1 (shown at top)
  const sectionedCouncilors = useMemo(() => {
    const sections: Record<string, CouncilorData[]> = {};
    councilorData.forEach(councilor => {
      if (councilor.order === 1) return; // Exclude order 1
      if (typeof councilor.order === 'number') {
        const orderStr = String(councilor.order);
        const sectionKey = orderStr[0];
        if (!sections[sectionKey]) sections[sectionKey] = [];
        sections[sectionKey].push(councilor);
      } else {
        if (!sections['other']) sections['other'] = [];
        sections['other'].push(councilor);
      }
    });
    Object.keys(sections).forEach(sectionKey => {
      sections[sectionKey].sort((a, b) => (a.order ?? 0) - (b.order ?? 0));
    });
    return sections;
  }, [councilorData]);

  // Function to render rich text content (simplified - no read more functionality)
  const renderContent = (content: any[]) => {
    if (!content || !Array.isArray(content)) {
      return <p>No content available</p>;
    }

    return (
      <div className="space-y-4">
        {content.map((block, index) => {
          if (block.type === "paragraph") {
            return (
              <p key={index} className="mb-4 text-sm text-muted-foreground">
                {block.children.map((child: any, childIndex: number) => (
                  <span key={childIndex}>{child.text}</span>
                ))}
              </p>
            );
          }
          return null;
        })}
      </div>
    );
  };

  // Function to get a brief excerpt for the card view (will be unused for main display)
  const getBriefExcerpt = (content: any[]) => {
    if (!content || !Array.isArray(content) || content.length === 0) {
      return "";
    }

    const firstParagraph = content[0];
    if (firstParagraph?.children && Array.isArray(firstParagraph.children)) {
      const text = firstParagraph.children
        .map((child: any) => child.text || "")
        .join("");
      // Return first 150 characters with ellipsis
      return text.length > 150 ? text.substring(0, 150) + "..." : text;
    }

    return "";
  };

  // Function to get councilor content (description or biography)
  const getCouncilorContent = (councilor: CouncilorData) => {
    // Prioritize description field, fallback to biography
    return councilor.description || councilor.biography || [];
  };

  // Function to open the modal with the selected councilor
  const openCouncilorDetails = (councilor: CouncilorData) => {
    setSelectedCouncilor(councilor);
    setIsModalOpen(true);
  }

  // Function to get optimal image URL
  const getOptimalImageUrl = (image: StrapiImage) => {
    if (image.formats?.large?.url) return image.formats.large.url;
    if (image.formats?.medium?.url) return image.formats.medium.url;
    if (image.formats?.small?.url) return image.formats.small.url;
    return image.url || "/logo.jpeg";
  };

  // Get best image URL for a councilor
  const getImageUrl = (councilor: CouncilorData) => {
    if (!councilor?.image) return "/logo.jpeg"

    if (Array.isArray(councilor.image)) {
      return councilor.image.length > 0 ? getOptimalImageUrl(councilor.image[0]) : "/logo.jpeg"
    } else {
      return getOptimalImageUrl(councilor.image)
    }
  }

  const getStaticTitle = () => {
    return t('pastCouncils.staticTitle');
  };

  const getStaticDescription = () => {
    return t('pastCouncils.staticDescription');
  };

  return (
    <div className="w-full px-4 py-12">
      {/* Year-based navigation tabs */}
      <div className="max-w-[2000px] mx-auto mb-8">
        <div className="flex flex-wrap gap-x-6 gap-y-2 text-xl font-bold pb-4 justify-center">
          {YEAR_TABS.map((tab, idx) => (
            <React.Fragment key={tab.key}>
              <button
                onClick={() => {
                
                  setActiveYear(tab.key);
                }}
                className={activeYear === tab.key ? 'text-[#232358] font-extrabold' : 'text-[#232358] underline underline-offset-2'}
                style={{ background: 'none', border: 'none', padding: 0, cursor: 'pointer' }}
              >
                {tab.label}
              </button>
              {idx < YEAR_TABS.length - 1 && <span className="text-gray-400 mx-2">|</span>}
            </React.Fragment>
          ))}
        </div>
      </div>

      {/* Display councilor with order 1 as the main title/description if present */}
      <div className="max-w-[2000px] mx-auto mb-8">
        {(() => {
          const councilOrder1 = councilorData.find(c => c.order === 1);
          if (!councilOrder1) return null;
          return (
            <>
              <div className="mb-8 mt-2">
                {Array.isArray(councilOrder1.description) && councilOrder1.description.length > 0 ? (
                  councilOrder1.description.map((p: any, idx: number) => (
                    <p key={idx} className="text-3xl md:text-4xl font-extrabold text-[#232358] mb-2">{p.children.map((child: any) => child.text).join('')}</p>
                  ))
                ) : null}
              </div>
              <h2 className="text-2xl font-bold text-[#232358] mb-2">{councilOrder1.name}</h2>
              {councilOrder1.order === 1 && <div className="border-b border-gray-300 mt-2" />}
            </>
          );
        })()}
      </div>

      <div className="max-w-[2000px] mx-auto">
        {loading ? (
          // Loading skeletons for the new stacked layout
          <div className="space-y-8">
            {Array(5).fill(null).map((_, index) => (
              <div key={`skeleton-group-${index}`} className="space-y-2">
                <Skeleton className="h-8 w-48" /> {/* Position title skeleton */}
                <Skeleton className="h-6 w-full" /> {/* Names list skeleton */}
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="col-span-full text-center py-16">
            <p className="text-2xl text-muted-foreground">{error}</p>
          </div>
        ) : Object.keys(sectionedCouncilors).length === 0 ? (
          <div className="col-span-full text-center py-16">
            <p className="text-2xl text-muted-foreground">{t('pastCouncils.noCouncilors')}</p>
          </div>
        ) : (
          <div className="space-y-12"> {/* Container for sections */}
            {Object.keys(sectionedCouncilors)
              .sort((a, b) => a.localeCompare(b, undefined, { numeric: true }))
              .map(sectionKey => (
                <div key={sectionKey} className="space-y-8">
                  {sectionedCouncilors[sectionKey].map((councilor, idx) => {
                    // Special UI for order 2 and 3 (like order 1)
                    if (councilor.order === 2 || councilor.order === 3) {
                      return (
                        <div key={councilor.id || idx} className="mb-8 mt-2">
                          {Array.isArray(councilor.description) && councilor.description.length > 0 ? (
                            councilor.description.map((p: any, i: number) => (
                              <p key={i} className="text-3xl md:text-4xl font-extrabold text-[#232358] mb-2">{p.children.map((child: any) => child.text).join('')}</p>
                            ))
                          ) : null}
                          <h3 className="text-2xl font-bold text-[#232358] mb-2">{councilor.name}</h3>
                          <div className="border-b border-gray-300 mt-2" />
                        </div>
                      );
                    }
                    // Default UI for other councilors
                    return (
                      <div key={councilor.id || idx} className="space-y-2">
                        {(councilor.name) && (
                          <h3 className="text-xl font-bold text-gray-800">
                            {councilor.name}
                          </h3>
                        )}
                        {Array.isArray(councilor.description) && councilor.description.length > 0 && (
                          councilor.description.map((p: any, i: number) => (
                            <p key={i} className="text-base text-gray-700">{p.children.map((child: any) => child.text).join('')}</p>
                          ))
                        )}
                      </div>
                    );
                  })}
                </div>
              ))}
          </div>
        )}
      </div>

      {/* Modal for individual councilor details (retained but no direct trigger in current stacked view) */}
      {selectedCouncilor && (
        <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
          <DialogContent className="sm:max-w-[800px] max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>{selectedCouncilor.name || `Councilor ${selectedCouncilor.id}`}</DialogTitle>
              {selectedCouncilor.description && selectedCouncilor.description.length > 0 && (
                <DialogDescription>
                  {selectedCouncilor.description.map(p => p.children.map((child: any) => child.text).join("")).join(" ")}
                </DialogDescription>
              )}
            </DialogHeader>
            <div className="grid gap-4 py-4">
              {/* Removed renderMediaContent call as images are not needed here */}
              {getCouncilorContent(selectedCouncilor).length > 0 && (
                <div className="prose max-w-none text-base">
                  {renderContent(getCouncilorContent(selectedCouncilor))}
                </div>
              )}
            </div>
          </DialogContent>
        </Dialog>
      )}
    </div>
  )
}

function CouncilorsLoading() {
  return (
    <div className="min-h-screen">
      <div className="relative h-96 bg-gradient-to-r from-primary to-primary/80 flex items-center justify-center">
        <div className="text-center text-white">
          <div className="h-8 bg-white/20 rounded animate-pulse w-64 mx-auto mb-4" />
          <div className="h-4 bg-white/20 rounded animate-pulse w-96 mx-auto" />
        </div>
      </div>

      <div className="w-full px-4 py-12">
        <div className="max-w-[2000px] mx-auto">
          <Card className="mb-12">
            <CardContent className="p-12">
              <div className="text-center space-y-6">
                <div className="h-10 bg-muted rounded animate-pulse w-48 mx-auto" />
                <div className="space-y-3">
                  <div className="h-6 bg-muted rounded animate-pulse" />
                  <div className="h-6 bg-muted rounded animate-pulse w-5/6 mx-auto" />
                  <div className="h-6 bg-muted rounded animate-pulse w-4/6 mx-auto" />
                </div>
              </div>
            </CardContent>
          </Card>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-12">
            {Array(8).fill(null).map((_, index) => (
              <Card key={`loading-${index}`} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="aspect-[4/3] bg-muted animate-pulse min-h-[400px]" />
                  <div className="p-12 space-y-6">
                    <div className="h-10 bg-muted rounded animate-pulse w-3/4" />
                    <div className="h-6 bg-muted rounded animate-pulse w-1/2" />
                    <div className="space-y-3">
                      <div className="h-4 bg-muted rounded animate-pulse" />
                      <div className="h-4 bg-muted rounded animate-pulse w-5/6" />
                      <div className="h-4 bg-muted rounded animate-pulse w-4/6" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Component that uses searchParams
function CouncilorsPage() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="pastCouncils.title"
        description="pastCouncils.description"
        image="/logo.jpeg"
        lang={currentLang}
      />

      <Suspense fallback={<CouncilorsLoading />}>
        <CouncilorsContent />
      </Suspense>
    </div>
  )
}

// Main export with Suspense wrapper
export default function Councilors() {
  return (
    <Suspense fallback={<CouncilorsLoading />}>
      <CouncilorsPage />
    </Suspense>
  )
}