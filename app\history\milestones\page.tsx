'use client'
import { Suspense, useMemo, useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import AnimatedHero from '@/components/animated-hero'
import PdfViewer from '@/components/pdf-viewer'
import { fetchMilestones } from '@/lib/strapi'
import { getTranslation, getLanguageFromParams } from '@/lib/translations'
import { getCachedPdfUrl, setCachedPdfUrl } from '@/lib/pdf-cache'

function MilestonesLoading() {
  return (
    <div className="min-h-screen flex items-center justify-center">
      <p>Loading...</p>
    </div>
  )
}

function MilestonesPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const [drivelink, setDrivelink] = useState<string | undefined>(undefined)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Map currentLang to Strapi locale
  const getStrapiLocale = useMemo(() => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK'
      case 'cn':
        return 'zh-Hans-HK'
      case 'en':
        return 'en'
      default:
        return undefined
    }
  }, [currentLang])

  useEffect(() => {
    async function fetchData() {
      const cacheKey = `milestones-${currentLang}`

      // Try cache first
      const cachedUrl = getCachedPdfUrl(cacheKey)
      if (cachedUrl) {
        setDrivelink(cachedUrl)
        setLoading(false)
        return
      }

      setLoading(true)
      setError(null)
      try {
        const milestones = await fetchMilestones({ locale: getStrapiLocale })
        const backendLink = (milestones.data as { drivelink?: string }[])?.[0]?.drivelink
        // Fallback to local PDF if backend link is missing or empty
        const finalUrl = backendLink && backendLink.trim() ? backendLink : '/pdf/KCC - milestone_compressed.pdf'

        setDrivelink(finalUrl)

        // Cache the URL
        setCachedPdfUrl(cacheKey, finalUrl)
      } catch (e) {
        setError(getTranslation('milestones.error', currentLang))
      } finally {
        setLoading(false)
      }
    }
    fetchData()
  }, [getStrapiLocale, currentLang])

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title={{
          zh: getTranslation('milestones.title', 'zh'),
          cn: getTranslation('milestones.title', 'cn'),
          en: getTranslation('milestones.title', 'en'),
        }}
        description={{
          zh: getTranslation('milestones.description', 'zh'),
          cn: getTranslation('milestones.description', 'cn'),
          en: getTranslation('milestones.description', 'en'),
        }}
        image="/logo.jpeg"
        lang={currentLang}
      />
      {loading ? (
        <MilestonesLoading />
      ) : error ? (
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>{error}</p>
        </div>
      ) : !drivelink ? (
        <div className="flex items-center justify-center min-h-[50vh]">
          <p>{getTranslation('milestones.error', currentLang) || 'PDF not available.'}</p>
        </div>
      ) : (
        <div className="relative z-0">
          <PdfViewer pdfUrl={drivelink} />
        </div>
      )}
    </div>
  )
}

export default function MilestonesPage() {
  return (
    <Suspense fallback={<MilestonesLoading />}>
      <MilestonesPageContent />
    </Suspense>
  )
}
