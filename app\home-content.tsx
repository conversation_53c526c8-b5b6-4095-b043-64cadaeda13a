"use client"

import <PERSON><PERSON><PERSON><PERSON> from "@/components/fancy-card"
import <PERSON><PERSON>ead<PERSON> from "@/components/section-header"
import { ChevronRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import Link from "next/link"
import AnimatedHero from "@/components/animated-hero"
import ChamberOverview from "@/components/chamber-overview"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import ChamberEventFeatures from "@/components/chamber-event-features"
import ChamberFeatures from "@/components/chamber-features"
import { motion } from "framer-motion"
import { useEffect, useState } from "react"
import { fetchContactInfo } from "@/lib/strapi"



const FADE_UP_ANIMATION_VARIANTS = {
  hidden: { opacity: 0, y: 10 },
  show: { opacity: 1, y: 0, transition: { type: "spring" as const } },
}

const stats = [
  {
    number: "85+",
    title: { zh: "年會歷史", en: "Years of History" },
  },
  {
    number: "1000+",
    title: { zh: "企業會員", en: "Corporate Members" },
  },
  {
    number: "200+",
    title: { zh: "年度活動", en: "Annual Events" },
  },
]

export default function HomeContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // --- Dynamic stats state ---
  const [statsData, setStatsData] = useState({
    yearofhistory: "85+",
    corporatemembers: "1000+",
    annualevents: "200+",
  })
  const [statsLoading, setStatsLoading] = useState(true)

  useEffect(() => {
    async function fetchStats() {
      try {
        let locale = "en";
        if (currentLang === "zh") locale = "zh-Hant-HK";
        else if (currentLang === "cn") locale = "zh-Hans-HK";
       

        setStatsLoading(true);
        const infos = await fetchContactInfo({ populate: "*", locale });
     

        if (!infos?.length) {
          console.warn("No stats data found for locale:", locale);
          setStatsLoading(false);
          return;
        }

        const main = infos[0];
        setStatsData({
          yearofhistory: main.yearofhistory ? main.yearofhistory + "+" : "85+",
          corporatemembers: main.corporatemembers ? main.corporatemembers + "+" : "1000+",
          annualevents: main.annualevents ? main.annualevents + "+" : "200+",
        });
        setStatsLoading(false);
      } catch (e) {
        console.error("Failed to fetch stats:", e);
        setStatsLoading(false);
      }
    }
    fetchStats();
  }, [currentLang]);

  return (
    <motion.div
      className="min-h-screen"
      initial="hidden"
      animate="show"
      viewport={{ once: true }}
      variants={{
        hidden: {},
        show: {
          transition: {
            staggerChildren: 0.15,
          },
        },
      }}
    >
      <AnimatedHero
        title="hero.title"
        description="hero.description"
        image="/logo.jpeg"
        height="large"
        lang={searchParams.get('lang') || 'en'}
      />
      {/* Chamber Event Features Section */}
      <motion.div variants={FADE_UP_ANIMATION_VARIANTS}>
        <ChamberEventFeatures />
      </motion.div>
      {/* Chamber Overview Section */}
      <motion.div variants={FADE_UP_ANIMATION_VARIANTS}>
        <ChamberOverview />
      </motion.div>

      {/* Chamber Features Section */}
      <motion.div variants={FADE_UP_ANIMATION_VARIANTS}>
        <ChamberFeatures />
      </motion.div>

      {/* Stats Section */}
      <motion.section
        variants={FADE_UP_ANIMATION_VARIANTS}
        className="bg-[#1E1B4B] text-white py-16"
      >
        <div className="container">
          {statsLoading ? (
            <div className="flex justify-center items-center min-h-[120px]">
              <span className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-white"></span>
            </div>
          ) : (
            <div className="grid md:grid-cols-3 gap-8">
              <motion.div
                variants={FADE_UP_ANIMATION_VARIANTS}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold mb-2">{statsData.yearofhistory}</div>
                <div className="text-lg text-white/80">
                  {t('footer.yearsOfHistory')}
                </div>
              </motion.div>
              <motion.div
                variants={FADE_UP_ANIMATION_VARIANTS}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold mb-2">{statsData.corporatemembers}</div>
                <div className="text-lg text-white/80">
                  {t('footer.corporateMembers')}
                </div>
              </motion.div>
              <motion.div
                variants={FADE_UP_ANIMATION_VARIANTS}
                className="text-center"
              >
                <div className="text-4xl md:text-5xl font-bold mb-2">{statsData.annualevents}</div>
                <div className="text-lg text-white/80">
                  {t('footer.annualEvents')}
                </div>
              </motion.div>
            </div>
          )}
        </div>
      </motion.section>
    </motion.div>
  )
}
