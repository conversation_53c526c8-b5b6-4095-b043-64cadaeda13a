"use client";
import { useTranslation, getLanguageFromParams } from "@/lib/translations";
import { useSearchParams, useRouter } from "next/navigation";
import { AuthForm } from "@/components/auth/auth-form";
import { useUser } from "@/contexts/user-context";
import { useEffect } from "react";
import Link from "next/link";
import { Suspense } from "react";

function LoginPageContent() {
  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);
  const { user } = useUser();
  const router = useRouter();

  useEffect(() => {
    if (user) {
      const returnUrl = searchParams.get("returnUrl");
      if (returnUrl) {
        router.push(decodeURIComponent(returnUrl));
      } else {
        router.push(`/dashboard?lang=${currentLang}`);
      }
    }
  }, [user, router, currentLang, searchParams]);

  if (user) {
    return null;
  }

  return (
    <div className="bg-gradient-to-br from-slate-100 to-slate-300 flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-md bg-white shadow-xl rounded-2xl p-8 sm:p-10">
        <AuthForm mode="login" />
        <p className="mt-6 text-center text-sm text-gray-600">
          {t("login.noAccount") || "Don't have an account?"}
          <Link
            href={`/register?lang=${currentLang}`}
            className="ml-1 font-semibold text-blue-600 hover:text-blue-500"
          >
            {t("login.signup") || "Sign up"}
          </Link>
        </p>
      </div>
    </div>
  );
}

export default function LoginPage() {
  return (
    <Suspense fallback={<div className="h-screen flex items-center justify-center text-lg">Loading...</div>}>
      <LoginPageContent />
    </Suspense>
  );
}
