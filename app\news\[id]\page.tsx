"use client"

import { useState, useEffect, use } from "react"
import { useSearchParams, usePathname } from "next/navigation"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Calendar, ArrowLeft, ChevronLeft, ChevronRight, ChevronUp, ChevronDown } from "lucide-react"
import Link from "next/link"
import { fetchNews } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import { useRef } from "react"
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import YouTubeEmbed from '@/components/ui/youtube-embed'

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId?: string
  name?: string
  url?: string
  formats?: StrapiImageFormats
  alternativeText?: string
  attributes?: {
    url?: string
    formats?: StrapiImageFormats
    alternativeText?: string
    [key: string]: any
  }
  isVideo?: boolean
  previewUrl?: string | null
  mime?: string
}

// For Strapi v4 format
interface StrapiAttributes {
  title?: string
  content?: any[]
  date?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  image?: {
    data: StrapiImage | StrapiImage[] | null
  }
  localizations?: any[]
  [key: string]: any
}

interface NewsItem {
  id: number
  title?: string
  content?: any[]
  date?: string
  createdAt?: string
  image?: {
    url: string
  } | StrapiImage[] | StrapiImage | null
  // Support for multiple images
  images?: StrapiImage[]
  // Consolidated media items after processing
  mediaItems?: StrapiImage[]
  order?: number | string // Allow string temporarily if conversion is complex
  // Support for Strapi v4 format
  attributes?: StrapiAttributes
  localizations?: any[]
  drivelink?: string
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to get image URL from different image formats
function getImageUrl(image: StrapiImage | { url: string } | null): string {
  if (!image) return "/logo.jpeg";

  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

  // For videos, return the preview URL if available
  if ('mime' in image && typeof image.mime === 'string' && image.mime.startsWith('video/')) {
    return ('previewUrl' in image && typeof image.previewUrl === 'string') ? image.previewUrl : "/logo.jpeg";
  }

  // If it's a simple object with url property
  if ('url' in image && typeof image.url === 'string') {
    return image.url.startsWith('http') ? image.url : `${strapiUrl}${image.url}`;
  }

  // If it's a StrapiImage
  const strapiImage = image as StrapiImage;

  // Try to get URL from formats
  if (strapiImage.formats) {
    if (strapiImage.formats.medium?.url) {
      return strapiImage.formats.medium.url.startsWith('http')
        ? strapiImage.formats.medium.url
        : `${strapiUrl}${strapiImage.formats.medium.url}`;
    } else if (strapiImage.formats.small?.url) {
      return strapiImage.formats.small.url.startsWith('http')
        ? strapiImage.formats.small.url
        : `${strapiUrl}${strapiImage.formats.small.url}`;
    } else if (strapiImage.formats.large?.url) {
      return strapiImage.formats.large.url.startsWith('http')
        ? strapiImage.formats.large.url
        : `${strapiUrl}${strapiImage.formats.large.url}`;
    } else if (strapiImage.formats.thumbnail?.url) {
      return strapiImage.formats.thumbnail.url.startsWith('http')
        ? strapiImage.formats.thumbnail.url
        : `${strapiUrl}${strapiImage.formats.thumbnail.url}`;
    }
  }

  // Try direct URL
  if (strapiImage.url) {
    return strapiImage.url.startsWith('http')
      ? strapiImage.url
      : `${strapiUrl}${strapiImage.url}`;
  }

  // Fallback to placeholder
  return "/logo.jpeg";
}

const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

export default function NewsDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const unwrappedParams = use(params)
  const id = unwrappedParams.id
  const searchParams = useSearchParams()
  const pathname = usePathname()

  const [newsItem, setNewsItem] = useState<NewsItem | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Get current language and translation function
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale from URL parameter
  const getStrapiLocale = () => {
    const lang = searchParams.get('lang')
    switch (lang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }

  // Helper function to create back link with language parameter
  const createBackLink = () => {
    const lang = searchParams.get('lang')
    if (lang) {
      return `/news?lang=${lang}`
    }
    return '/news'
  }

  useEffect(() => {
    const loadNewsDetail = async () => {
      try {
        setLoading(true)
        // Get the current locale
        const locale = getStrapiLocale()

        // Fetch the specific news item by ID, including localizations
        const response = await fetchNews({
          populate: "*", // fetch all fields
          locale: locale, // Add locale parameter
          filters: {
            id: {
              $eq: id
            }
          }
        })

        if (response.data && response.data.length > 0) {
          const item = response.data[0];
          const processedItem: NewsItem = { ...item };

    

          // Handle Strapi v4 format where data comes with attributes
          if (item.attributes) {
            const { image, localizations, ...restAttributes } = item.attributes;
            Object.assign(processedItem, restAttributes);
            processedItem.id = item.id;
            // Store localizations
            processedItem.localizations = localizations || [];

            // Handle image in Strapi v4 format
            if (image && image.data) {
              const imageData = (image.data as any);
              if (Array.isArray(imageData)) {
                processedItem.mediaItems = imageData.map((img: any) => ({
                  id: img.id,
                  ...img.attributes
                }));
              } else {
                processedItem.mediaItems = [{
                  id: imageData.id,
                  ...imageData.attributes
                }];
              }
            }
          }
          // Handle direct image array or single object (older Strapi versions or different field name)
          else if (item.image || item.images) {
            const rawMedia = (item.images || item.image) as StrapiImage | StrapiImage[];
            const mediaArray = Array.isArray(rawMedia) ? rawMedia : (rawMedia ? [rawMedia] : []);

            processedItem.mediaItems = mediaArray.map(img => ({
              id: img.id,
              url: img.url,
              formats: img.formats,
              mime: img.mime, // Include mime for video detection
              previewUrl: img.previewUrl, // Include previewUrl for videos
            }));
          }

          // Ensure order is a number if possible
          if (processedItem.order && typeof processedItem.order === 'string') {
            processedItem.order = parseInt(processedItem.order, 10);
            if (isNaN(processedItem.order)) {
              delete processedItem.order; // Remove if conversion fails
            }
          }

         
          setNewsItem(processedItem);
        } else {
          setError("News article not found")
        }
      } catch (err: any) {
        console.error("Error fetching news detail:", err)
        setError(err.message || "Failed to load news article")
      } finally {
        setLoading(false)
      }
    }

    loadNewsDetail()
  }, [id, searchParams]) // Re-run when id or search params (including lang) change

  // Custom language switcher for news detail page
  const availableLangs = [
    { code: 'en', label: 'EN' },
    { code: 'zh', label: '繁' },
    { code: 'cn', label: '简' },
  ];
  const langToLocale: Record<string, string> = {
    en: 'en',
    zh: 'zh-Hant-HK',
    cn: 'zh-Hans-HK',
  };
  const getLocalizedId = (lang: string) => {
    if (!newsItem) return null;
    const targetLocale = langToLocale[lang];
    if ((newsItem?.locale || newsItem?.attributes?.locale) === targetLocale) return id;
    if (newsItem?.localizations) {
      const match = newsItem.localizations.find((loc: any) => loc.locale === targetLocale);
      return match ? match.id : null;
    }
    // Fallback: check attributes.locale if present
    if (newsItem?.attributes?.locale === targetLocale) return id;
    return null;
  };

  if (error) {
    return (
      <div className="container py-12">
        <div className="bg-red-50 text-red-500 p-4 rounded-md">
          {error}
        </div>
        <div className="mt-4">
          <Link href={createBackLink()}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('news.backToNews')}
            </Button>
          </Link>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      <div className="container py-12">
        {/* Custom Language Switcher */}
        <div className="flex gap-2 mb-4 justify-end">
          {availableLangs.map(({ code, label }) => {
            const targetId = getLocalizedId(code);
            const isActive = currentLang === code;
            return targetId ? (
              <Link
                key={code}
                href={`/news/${targetId}?lang=${code}`}
                className={`px-3 py-1 rounded ${isActive ? 'bg-blue-900 text-white font-bold' : 'bg-gray-100 text-gray-700 hover:bg-blue-100'}`}
              >
                {label}
              </Link>
            ) : (
              <span key={code} className="px-3 py-1 rounded bg-gray-200 text-gray-400 cursor-not-allowed">{label}</span>
            );
          })}
        </div>
        <div className="mb-6">
          <Link href={createBackLink()}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('news.backToNews')}
            </Button>
          </Link>
        </div>

        {loading ? (
          <div className="space-y-8">
            <Skeleton className="h-10 w-3/4" />
            <div className="flex items-center gap-2">
              <Skeleton className="h-4 w-32" />
            </div>
            <Skeleton className="h-[400px] w-full" />
            <div className="space-y-4">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-3/4" />
            </div>
          </div>
        ) : newsItem ? (
          <article className="prose prose-lg max-w-none">
            <h1>{newsItem.title || newsItem.attributes?.title}</h1>
            <div className="flex items-center gap-2 text-muted-foreground mb-6">
              <Calendar className="h-4 w-4" />
              <time dateTime={newsItem.date || newsItem.createdAt || newsItem.attributes?.date || newsItem.attributes?.createdAt || undefined}>
                {new Date(newsItem.date || newsItem.createdAt || newsItem.attributes?.date || newsItem.attributes?.createdAt || Date.now()).toLocaleDateString()}
              </time>
            </div>

            {/* Media Section */}
            {(() => {
              const mediaList = patchMediaMime(newsItem?.mediaItems || []);
              if (mediaList.length > 0) {
                return (
                  <div className="relative w-full aspect-[4/3] rounded-lg overflow-hidden bg-gray-100 mb-8">
                    {mediaList.length > 1 ? (
                      <FullImageSlider images={mediaList} alt={newsItem.title || newsItem.attributes?.title || "News Media"} interval={3000} />
                    ) : (
                      (() => {
                        const currentMedia = mediaList[0];
                        return currentMedia.mime?.startsWith('video/') ? (
                          <video
                            src={currentMedia.url}
                            poster={currentMedia.previewUrl}
                            controls
                            className="w-full h-full object-contain"
                            playsInline
                          />
                        ) : (
                          <Image
                            src={getImageUrl(currentMedia)}
                            alt={newsItem.title || newsItem.attributes?.title || "News Image"}
                            fill
                            className="object-contain"
                            sizes="(max-width: 768px) 100vw, 50vw"
                          />
                        );
                      })()
                    )}
                  </div>
                );
              }
              return null;
            })()}

            {/* Google Drive Embed Section */}
            {(() => {
              const drivelink = newsItem?.drivelink || newsItem?.attributes?.drivelink;
              if (isGoogleDriveLink(drivelink)) {
                return (
                  <div className="mb-8">
                    <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                      <GoogleDriveEmbed videoId={drivelink} title={newsItem?.title || newsItem?.attributes?.title || 'Google Drive Video'} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                    </div>
                  </div>
                );
              } else if (isYouTubeLink(drivelink)) {
                return (
                  <div className="mb-8">
                    <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                      <YouTubeEmbed videoId={drivelink} title={newsItem?.title || newsItem?.attributes?.title || 'YouTube Video'} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                    </div>
                  </div>
                );
              }
              return null;
            })()}

            {/* Content */}
            {(newsItem.content && newsItem.content.length > 0) || (newsItem.attributes?.content && newsItem.attributes.content.length > 0) ? (
              (newsItem.content || newsItem.attributes?.content).map((block: any, index: number) => {
                if (block.type === "paragraph") {
                  return (
                    <p key={index}>
                      {block.children?.map((child: any, childIndex: number) => (
                        <span key={childIndex}>{child.text}</span>
                      ))}
                    </p>
                  )
                }
                // Add other block types here if needed
                return null
              })
            ) : (
              (() => {
                const noContentMsg = t('news.noContent' as any);
                // Only show if translation exists and is not just the key itself
                if (!noContentMsg || noContentMsg === 'news.noContent' || noContentMsg.startsWith('Translation not found')) return null;
                return <p className="text-muted-foreground">{noContentMsg}</p>;
              })()
            )}
          </article>
        ) : (
          <div className="text-center py-12">
            <p className="text-muted-foreground">News article not found.</p>
          </div>
        )}

        <div className="mt-8">
          <Link href={createBackLink()}>
            <Button variant="outline">
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t('news.backToNews')}
            </Button>
          </Link>
        </div>
      </div>
    </div>
  )
}
