"use client"

import { useState, useEffect, useRef } from "react"
import { useSearchParams } from "next/navigation"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, ChevronRight, ChevronLeft, ChevronUp, ChevronDown } from "lucide-react"
import Link from "next/link"
import AnimatedHero from "@/components/animated-hero"
import { fetchNews } from "@/lib/strapi"
import { Skeleton } from "@/components/ui/skeleton"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import GoogleDriveEmbed from '@/components/ui/google-drive-embed'
import YouTubeEmbed from '@/components/ui/youtube-embed'

interface StrapiImageFormat {
  url: string
  width?: number
  height?: number
}

interface StrapiImageFormats {
  large?: StrapiImageFormat
  medium?: StrapiImageFormat
  small?: StrapiImageFormat
  thumbnail?: StrapiImageFormat
}

interface StrapiImage {
  id: number
  documentId?: string
  name?: string
  url?: string
  formats?: StrapiImageFormats
  alternativeText?: string
  attributes?: {
    url?: string
    formats?: StrapiImageFormats
    alternativeText?: string
    [key: string]: any
  }
  isVideo?: boolean
  previewUrl?: string | null
  mime?: string
}

// For Strapi v4 format
interface StrapiAttributes {
  title?: string
  content?: any[]
  date?: string
  createdAt?: string
  updatedAt?: string
  publishedAt?: string
  image?: {
    data: StrapiImage | StrapiImage[] | null
  }
  [key: string]: any
}

interface NewsItem {
  id: number
  title?: string
  content?: any[]
  date?: string
  createdAt?: string
  image?: {
    url: string
  } | StrapiImage[] | StrapiImage | null
  // Support for multiple images
  images?: StrapiImage[]
  order?: number
  // Support for Strapi v4 format
  attributes?: StrapiAttributes
}

interface StrapiResponse {
  data: NewsItem[]
  meta: {
    pagination: {
      page: number
      pageSize: number
      pageCount: number
      total: number
    }
  }
}

// Patch mime for .mp4 files if missing
function patchMediaMime(mediaArr: any[]): any[] {
  return mediaArr.map((item: any) =>
    item.mime
      ? item
      : { ...item, mime: item.url && item.url.endsWith('.mp4') ? 'video/mp4' : 'image/jpeg' }
  );
}

// Local FullImageSlider for multiple images/videos
function FullImageSlider({ images, alt, interval = 3000 }: {
  images: any[]
  alt: string
  interval?: number
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const videoRef = useRef<HTMLVideoElement | null>(null)

  // Handlers for arrows
  const goToPrev = () => setCurrentIndex((prev) => (prev - 1 + images.length) % images.length)
  const goToNext = () => setCurrentIndex((prev) => (prev + 1) % images.length)

  useEffect(() => {
    if (images.length <= 1) return

    const currentMedia = images[currentIndex]
    let timer: NodeJS.Timeout | null = null
    let videoEl: HTMLVideoElement | null = null

    if (currentMedia.mime?.startsWith('video/')) {
      videoEl = videoRef.current
      if (videoEl) {
        const handleEnded = () => {
          goToNext()
        }
        videoEl.addEventListener('ended', handleEnded)
        return () => {
          videoEl && videoEl.removeEventListener('ended', handleEnded)
        }
      }
    } else {
      timer = setInterval(() => {
        goToNext()
      }, interval)
      return () => {
        if (timer) clearInterval(timer)
      }
    }
  }, [images, currentIndex, interval])

  if (images.length === 0) {
    return (
      <div className="relative w-full aspect-[4/3] bg-gray-200 flex items-center justify-center">
        <p className="text-muted-foreground">No media available</p>
      </div>
    )
  }

  const currentMedia = images[currentIndex]

  return (
    <div className="relative w-full aspect-[4/3] bg-gray-50 overflow-hidden flex items-center justify-center">
      {/* Left Arrow */}
      {images.length > 1 && (
        <button
          className="absolute left-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToPrev}
          aria-label="Previous slide"
        >
          <ChevronLeft className="w-6 h-6" />
        </button>
      )}

      {/* Current Media */}
      <div className="relative w-full h-full flex items-center justify-center">
        {currentMedia.mime?.startsWith('video/') ? (
          <video
            ref={videoRef}
            src={currentMedia.url}
            poster={currentMedia.previewUrl}
            controls
            className="w-full h-full object-contain"
            playsInline
            autoPlay={false}
          />
        ) : (
          <Image
            src={currentMedia.url}
            alt={`${alt} - media ${currentIndex + 1}`}
            fill
            className="object-contain"
            sizes="(max-width: 768px) 100vw, 50vw"
            priority
          />
        )}
      </div>

      {/* Right Arrow */}
      {images.length > 1 && (
        <button
          className="absolute right-2 top-1/2 -translate-y-1/2 z-10 bg-white/80 hover:bg-white rounded-full p-1 shadow"
          onClick={goToNext}
          aria-label="Next slide"
        >
          <ChevronRight className="w-6 h-6" />
        </button>
      )}

      {/* Dots indicator for multiple media items */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center z-20 pointer-events-none">
          <div className="flex gap-1.5 bg-black/40 rounded-full px-2 py-1">
            {images.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentIndex ? "bg-white" : "bg-white/50"
                } pointer-events-auto`}
                onClick={() => setCurrentIndex(index)}
                aria-label={`Go to slide ${index + 1}`}
              />
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

// Helper function to get image URL with fallback
const getImageUrl = (strapiImage: StrapiImage | undefined): string => {
  if (!strapiImage) return "/logo.jpeg";

  const strapiUrl = process.env.NEXT_PUBLIC_STRAPI_URL || 'https://strapibackendproject-3x6s.onrender.com';

  // For videos, return the preview URL if available
  if (strapiImage.mime?.startsWith('video/')) {
    return strapiImage.previewUrl || "/logo.jpeg";
  }

  // For images, try different format sizes
  if (strapiImage.formats?.medium?.url) {
    return strapiImage.formats.medium.url.startsWith('http')
      ? strapiImage.formats.medium.url
      : `${strapiUrl}${strapiImage.formats.medium.url}`;
  }
  if (strapiImage.formats?.small?.url) {
    return strapiImage.formats.small.url.startsWith('http')
      ? strapiImage.formats.small.url
      : `${strapiUrl}${strapiImage.formats.small.url}`;
  }
  if (strapiImage.url) {
    return strapiImage.url.startsWith('http')
      ? strapiImage.url
      : `${strapiUrl}${strapiImage.url}`;
  }
  // Fallback to placeholder
  return "/logo.jpeg";
}

// Place these helpers after imports, before the component
const isGoogleDriveLink = (url: string): boolean => typeof url === 'string' && url.includes('drive.google.com');
const isYouTubeLink = (url: string): boolean => typeof url === 'string' && (url.includes('youtube.com') || url.includes('youtu.be'));

export default function NewsContent() {
  const searchParams = useSearchParams()
  const [latestNews, setLatestNews] = useState<NewsItem[]>([])
  const [regularNews, setRegularNews] = useState<NewsItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)
  const [activeCategory] = useState("all")

  // Get current language and translation function
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // Helper function to get Strapi locale from URL parameter
  const getStrapiLocale = () => {
    const lang = searchParams.get('lang')
    switch (lang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }

  // Helper function to create news detail link with language parameter
  const createNewsLink = (newsId: number) => {
    const lang = searchParams.get('lang')
    if (lang) {
      return `/news/${newsId}?lang=${lang}`
    }
    return `/news/${newsId}`
  }

  const loadNews = async (page = 1, category = "all") => {
    try {
      // Get the current locale
      const locale = getStrapiLocale()

      // First, fetch latest news (last 4 weeks)
      const fourWeeksAgo = new Date();
      fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28);

      const latestResponse = await fetchNews({
        populate: "*",
        locale: locale, // Add locale parameter
        pagination: { page: 1, pageSize: 5000 }, // Fetch up to 5000 latest news
        filters: {
          date: {
            $gte: fourWeeksAgo.toISOString()
          }
        }
      }) as StrapiResponse;

      // Then fetch regular news
      const regularResponse = await fetchNews({
        populate: "*",
        locale: locale, // Add locale parameter
        pagination: { page, pageSize: 5000 }, // Fetch up to 5000 regular news
        filters: {
          date: {
            $lt: fourWeeksAgo.toISOString()
          }
        },
        ...(category !== "all" && {
          filters: {
            category: {
              $eq: category
            }
          }
        })
      }) as StrapiResponse;

      // Process latest news
      const latestProcessedData = latestResponse.data.map(item => {
        const processedItem = { ...item };
        if (item.attributes) {
          Object.assign(processedItem, item.attributes);
          processedItem.id = item.id;
          if (item.attributes.image && item.attributes.image.data) {
            if (Array.isArray(item.attributes.image.data)) {
              processedItem.images = item.attributes.image.data.map(img => ({
                id: img.id,
                ...img.attributes
              }));
            } else {
              processedItem.image = {
                id: item.attributes.image.data.id,
                ...item.attributes.image.data.attributes
              };
            }
          }
        } else if (item.image && Array.isArray(item.image) && item.image.length > 0) {
          processedItem.images = item.image.map(img => ({
            id: img.id,
            url: img.url,
            formats: img.formats
          }));
        }
        return processedItem;
      });

      // Process regular news
      const regularProcessedData = regularResponse.data.map(item => {
        const processedItem = { ...item };
        if (item.attributes) {
          Object.assign(processedItem, item.attributes);
          processedItem.id = item.id;
          if (item.attributes.image && item.attributes.image.data) {
            if (Array.isArray(item.attributes.image.data)) {
              processedItem.images = item.attributes.image.data.map(img => ({
                id: img.id,
                ...img.attributes
              }));
            } else {
              processedItem.image = {
                id: item.attributes.image.data.id,
                ...item.attributes.image.data.attributes
              };
            }
          }
        } else if (item.image && Array.isArray(item.image) && item.image.length > 0) {
          processedItem.images = item.image.map(img => ({
            id: img.id,
            url: img.url,
            formats: img.formats
          }));
        }
        return processedItem;
      });

      if (page === 1) {
        // Set all latest news and first page of regular news
        setLatestNews(latestProcessedData);
        setRegularNews(regularProcessedData);

        if (latestProcessedData.length === 0 && regularProcessedData.length === 0) {
          setError("No news items available. Please check back later.")
        }
      } else {
        // Only append regular news on pagination
        setRegularNews(prev => [...prev, ...regularProcessedData]);
      }

      setHasMore(regularResponse.meta.pagination.page < regularResponse.meta.pagination.pageCount);

      // Log for debugging
  
    } catch (err: any) {
      console.error("Error fetching news:", err)
      setError(err.message || "Failed to load news")
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    setLoading(true)
    setCurrentPage(1)
    loadNews(1, "all")
  }, [searchParams]) // Re-run when search params (including lang) change

  const handleLoadMore = () => {
    const nextPage = currentPage + 1
    setCurrentPage(nextPage)
    loadNews(nextPage, "all")
  }

  // Sort news by date descending before rendering
  const sortedLatestNews = [...latestNews].sort((a, b) => {
    const dateA = new Date(a.date || a.createdAt || 0).getTime();
    const dateB = new Date(b.date || b.createdAt || 0).getTime();
    return dateB - dateA;
  });
  const sortedRegularNews = [...regularNews].sort((a, b) => {
    const dateA = new Date(a.date || a.createdAt || 0).getTime();
    const dateB = new Date(b.date || b.createdAt || 0).getTime();
    return dateB - dateA;
  });

  if (error && error !== "No news items available. Please check back later.") {
    return (
      <div className="min-h-screen">
        {/* Hero Section */}
        <AnimatedHero
          title="news.title"
          description="news.description"
          image="/logo.jpeg"
          lang={searchParams.get('lang') || 'en'}
        />
        <div className="container py-12">
          <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100">
            <h2 className="text-xl font-semibold text-red-700 mb-2">
              <span className="block">發生錯誤</span>
              <span className="text-base text-red-600">An error occurred</span>
            </h2>
            <p className="text-red-600">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <AnimatedHero
        title="news.title"
        description="news.description"
        image="/logo.jpeg"
        lang={searchParams.get('lang') || 'en'}
      />

      <div className="container py-12">
        {/* News Listing */}
        <section>
          <Tabs value={activeCategory} className="space-y-8">
            <TabsContent value={activeCategory} className="space-y-12">
              {loading && currentPage === 1 ? (
                <div className="grid md:grid-cols-2 gap-6">
                  {Array(4).fill(null).map((_, index) => (
                    <Card key={`skeleton-${index}`} className="overflow-hidden">
                      <div className="relative aspect-video">
                        <Skeleton className="absolute inset-0" />
                      </div>
                      <CardContent className="p-6 space-y-4">
                        <Skeleton className="h-4 w-32" />
                        <Skeleton className="h-6 w-3/4" />
                        <Skeleton className="h-16 w-full" />
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <>
                  {/* Latest News Section (less than 4 weeks old) */}
                  {sortedLatestNews.length > 0 && (
                    <div className="space-y-6">
                      <div className="border-b pb-2">
                        <h2 className="text-2xl font-bold">
                          <span className="text-primary">{t('news.latest')}</span>
                        </h2>
                        <p className="text-muted-foreground">{t('news.latestDescription')}</p>
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        {sortedLatestNews.map((news) => (
                          <Card key={news.id} className="overflow-hidden hover:shadow-lg transition-all border-primary border-2">
                            {(() => {
                              const mediaList = patchMediaMime(Array.isArray(news.image) ? news.image : (news.image ? [news.image] : []));
                              const link = (news as any).drivelink ?? '';
                              const hasDrive = isGoogleDriveLink(link);
                              const hasYouTube = isYouTubeLink(link);
                              const hasMedia = mediaList.length > 0;
                              const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';
                              if (hasMedia || hasDrive || hasYouTube) {
                                return (
                                  <Tabs defaultValue={defaultTab} className="w-full">
                                    <TabsList className="flex w-full mb-2">
                                      {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                                      {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                                      {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                                    </TabsList>
                                    {hasMedia && (
                                      <TabsContent value="media">
                                        <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden">
                                          {mediaList.length > 1 ? (
                                            <FullImageSlider images={mediaList} alt={news.title || 'News Media'} interval={3000} />
                                          ) : (
                                            mediaList[0].mime?.startsWith('video/') ? (
                                              <video
                                                src={mediaList[0].url}
                                                poster={mediaList[0].previewUrl || undefined}
                                                controls
                                                className="w-full h-full object-contain"
                                                playsInline
                                              />
                                            ) : (
                                              <Image
                                                src={getImageUrl(mediaList[0])}
                                                alt={news.title || 'News Image'}
                                                fill
                                                className="object-contain"
                                                sizes="(max-width: 768px) 100vw, 50vw"
                                              />
                                            )
                                          )}
                                        </div>
                                      </TabsContent>
                                    )}
                                    {hasDrive && (
                                      <TabsContent value="drive">
                                        <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                          <GoogleDriveEmbed videoId={link} title={news.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                        </div>
                                      </TabsContent>
                                    )}
                                    {hasYouTube && (
                                      <TabsContent value="youtube">
                                        <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                          <YouTubeEmbed videoId={link} title={news.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                        </div>
                                      </TabsContent>
                                    )}
                                  </Tabs>
                                );
                              } else {
                                return (
                                  <div className="flex items-center justify-center aspect-[4/3] bg-gray-100">
                                    <p className="text-muted-foreground p-4 text-center">No media available</p>
                                  </div>
                                );
                              }
                            })()}
                            <CardContent className="p-6">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                                <Calendar className="h-4 w-4" />
                                <time>{new Date(news.date || news.createdAt || Date.now()).toLocaleDateString()}</time>
                              </div>
                              <h3 className="text-xl font-bold mb-2">{news.title}</h3>
                              <div className="text-muted-foreground line-clamp-2">
                                {news.content?.[0]?.children?.[0]?.text}
                              </div>
                              <Link href={createNewsLink(news.id)}>
                                <Button className="mt-4">
                                  <div className="flex items-center">
                                    <span className="text-sm">{t('news.readMore')}</span>
                                    <ChevronRight className="h-4 w-4 ml-1" />
                                  </div>
                                </Button>
                              </Link>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Regular News Section (more than 4 weeks old) */}
                  {sortedRegularNews.length > 0 && (
                    <div className="space-y-6">
                      <div className="border-b pb-2">
                        <h2 className="text-2xl font-bold">
                          <span>{t('news.archive')}</span>
                        </h2>
                        <p className="text-muted-foreground">{t('news.archiveDescription')}</p>
                      </div>

                      <div className="grid md:grid-cols-2 gap-6">
                        {sortedRegularNews.map((news) => (
                          <Card key={news.id} className="overflow-hidden hover:shadow-lg transition-all">
                            {(() => {
                              const mediaList = patchMediaMime(Array.isArray(news.image) ? news.image : (news.image ? [news.image] : []));
                              const link = (news as any).drivelink ?? '';
                              const hasDrive = isGoogleDriveLink(link);
                              const hasYouTube = isYouTubeLink(link);
                              const hasMedia = mediaList.length > 0;
                              const defaultTab = hasMedia ? 'media' : hasDrive ? 'drive' : hasYouTube ? 'youtube' : 'none';
                              if (hasMedia || hasDrive || hasYouTube) {
                                return (
                                  <Tabs defaultValue={defaultTab} className="w-full">
                                    <TabsList className="flex w-full mb-2">
                                      {hasMedia && <TabsTrigger value="media" className="flex-1">{t('reception.mediaTab')}</TabsTrigger>}
                                      {hasDrive && <TabsTrigger value="drive" className="flex-1">{t('reception.driveTab')}</TabsTrigger>}
                                      {hasYouTube && <TabsTrigger value="youtube" className="flex-1">YouTube</TabsTrigger>}
                                    </TabsList>
                                    {hasMedia && (
                                      <TabsContent value="media">
                                        <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px] flex items-center justify-center bg-gray-50 overflow-hidden">
                                          {mediaList.length > 1 ? (
                                            <FullImageSlider images={mediaList} alt={news.title || 'News Media'} interval={3000} />
                                          ) : (
                                            mediaList[0].mime?.startsWith('video/') ? (
                                              <video
                                                src={mediaList[0].url}
                                                poster={mediaList[0].previewUrl || undefined}
                                                controls
                                                className="w-full h-full object-contain"
                                                playsInline
                                              />
                                            ) : (
                                              <Image
                                                src={getImageUrl(mediaList[0])}
                                                alt={news.title || 'News Image'}
                                                fill
                                                className="object-contain"
                                                sizes="(max-width: 768px) 100vw, 50vw"
                                              />
                                            )
                                          )}
                                        </div>
                                      </TabsContent>
                                    )}
                                    {hasDrive && (
                                      <TabsContent value="drive">
                                        <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                          <GoogleDriveEmbed videoId={link} title={news.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                        </div>
                                      </TabsContent>
                                    )}
                                    {hasYouTube && (
                                      <TabsContent value="youtube">
                                        <div className="relative aspect-w-16 aspect-h-12 w-full min-h-[300px]">
                                          <YouTubeEmbed videoId={link} title={news.title || ''} className="absolute inset-0 w-full h-full rounded-lg shadow-sm" />
                                        </div>
                                      </TabsContent>
                                    )}
                                  </Tabs>
                                );
                              } else {
                                return (
                                  <div className="flex items-center justify-center aspect-[4/3] bg-gray-100">
                                    <p className="text-muted-foreground p-4 text-center">No media available</p>
                                  </div>
                                );
                              }
                            })()}
                            <CardContent className="p-6">
                              <div className="flex items-center gap-2 text-sm text-muted-foreground mb-2">
                                <Calendar className="h-4 w-4" />
                                <time>{new Date(news.date || news.createdAt || Date.now()).toLocaleDateString()}</time>
                              </div>
                              <h3 className="text-xl font-bold mb-2">{news.title}</h3>
                              <div className="text-muted-foreground line-clamp-2">
                                {news.content?.[0]?.children?.[0]?.text}
                              </div>
                              <Link href={createNewsLink(news.id)}>
                                <Button className="mt-4">
                                  <div className="flex items-center">
                                    <span className="text-sm">{t('news.readMore')}</span>
                                    <ChevronRight className="h-4 w-4 ml-1" />
                                  </div>
                                </Button>
                              </Link>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* No News Message */}
                  {(sortedLatestNews.length === 0 && sortedRegularNews.length === 0 && !loading) || error === "No news items available. Please check back later." ? (
                    <div className="text-center py-12">
                      <p className="text-muted-foreground">{t('news.noNews')}</p>
                    </div>
                  ) : null}
                </>
              )}

              {hasMore && (
                <div className="flex justify-center mt-8">
                  <Button
                    variant="outline"
                    onClick={handleLoadMore}
                    disabled={loading}
                  >
                    {loading ? t('news.loading') : t('news.loadMore')}
                  </Button>
                </div>
              )}
            </TabsContent>
          </Tabs>
        </section>
      </div>
    </div>
  )
}
