"use client";
import { useTranslation, getLanguageFromParams } from "@/lib/translations";
import { useSearchParams } from "next/navigation";
import { AuthForm } from "@/components/auth/auth-form";
import Link from "next/link";
import { Suspense } from "react";

function RegisterPageContent() {
  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);

  return (
    <div className="bg-gradient-to-br from-slate-100 to-slate-300 flex items-center justify-center px-4 py-8">
      <div className="w-full max-w-xs sm:max-w-sm md:max-w-md bg-white shadow-xl rounded-2xl p-8 sm:p-10">
        <AuthForm mode="register" />
        <p className="mt-6 text-center text-sm text-gray-600">
          {t("register.haveAccount") || "Already have an account?"}
          <Link
            href={`/login?lang=${currentLang}`}
            className="ml-1 font-semibold text-blue-600 hover:text-blue-500"
          >
            {t("register.signIn") || "Sign in"}
          </Link>
        </p>
      </div>
    </div>
  );
}

export default function RegisterPage() {
  return (
    <Suspense fallback={<div>Loading registration...</div>}>
      <RegisterPageContent />
    </Suspense>
  );
}