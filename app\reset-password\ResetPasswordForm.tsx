"use client";
import { useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { fetchAPI } from "@/strapi";
import { useTranslation, getLanguageFromParams } from "@/lib/translations";

export default function ResetPasswordForm() {
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [submitted, setSubmitted] = useState(false);
  const searchParams = useSearchParams();
  const code = searchParams.get("code");
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);
  const router = useRouter();

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (password !== confirmPassword) {
      toast.error(t("resetPassword.passwordsDoNotMatch"));
      return;
    }
    setIsLoading(true);
    try {
      await fetchAPI("auth/reset-password", {}, {
        method: "POST",
        body: JSON.stringify({ code, password, passwordConfirmation: confirmPassword }),
      });
      setSubmitted(true);
      toast.success(t("resetPassword.success"));
      setTimeout(() => {
        if (currentLang === "zh") {
          router.push("/login?lang=zh");
        } else if (currentLang === "cn") {
          router.push("/login?lang=cn");
        } else {
          router.push("/login");
        }
      }, 2000);
    } catch (error) {
      toast.error(t("resetPassword.error"));
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-100 to-slate-300 px-2 sm:px-4 py-8">
      <div className="w-full max-w-md bg-white shadow-xl rounded-2xl p-4 sm:p-8 md:p-10 mx-auto">
        <h2 className="text-2xl sm:text-3xl font-bold mb-4 text-center">{t("resetPassword.title")}</h2>
        {submitted ? (
          <p className="text-center text-green-600">{t("resetPassword.successRedirect")}</p>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            <div>
              <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-1">
                {t("resetPassword.newPassword")}
              </label>
              <Input
                id="password"
                type="password"
                autoComplete="new-password"
                required
                value={password}
                onChange={e => setPassword(e.target.value)}
                placeholder={t("resetPassword.newPasswordPlaceholder")}
                className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition"
              />
            </div>
            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-700 mb-1">
                {t("resetPassword.confirmNewPassword")}
              </label>
              <Input
                id="confirmPassword"
                type="password"
                autoComplete="new-password"
                required
                value={confirmPassword}
                onChange={e => setConfirmPassword(e.target.value)}
                placeholder={t("resetPassword.confirmNewPasswordPlaceholder")}
                className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition"
              />
            </div>
            <Button
              type="submit"
              className="w-full bg-[#1E1B4B] hover:bg-[#2d2870] text-white py-3 text-lg font-semibold rounded-lg shadow transition-all duration-150"
              disabled={isLoading}
            >
              {isLoading ? t("resetPassword.resetting") : t("resetPassword.button")}
            </Button>
          </form>
        )}
      </div>
    </div>
  );
} 