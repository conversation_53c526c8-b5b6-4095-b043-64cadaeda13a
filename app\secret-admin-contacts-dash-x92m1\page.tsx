'use client';

import { useEffect, useState, Suspense } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useSearchParams } from 'next/navigation';
import { useTranslation, getLanguageFromParams, TranslationKey } from '@/lib/translations';
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem
} from '@/components/ui/select';
import { toast } from "sonner";

interface Registration {
  id: number;
  documentId: string;
  Name: string;
  email: string;
  Number: string;
  EventName: string;
  OrganizationName: string;
  createdAt: string;
  updatedAt: string;
  publishedAt: string;
  locale: string;
  eventDocumentId: string;
  statusreg?: 'Registered' | 'Cancelled' | 'Attended';
}

interface GroupedRegistrations {
  [key: string]: Registration[];
}

function ContactSubmissionsPageContent() {
  const [registrations, setRegistrations] = useState<Registration[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());

  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);

  useEffect(() => {
    const fetchRegistrations = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/registrations`, {
          headers: {
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
            'Content-Type': 'application/json',
          },
        });

        if (!response.ok) {
          throw new Error('Failed to authenticate or fetch data');
        }

        const result = await response.json();
        
        if (result.data) {
          // Deduplicate registrations by documentId, keeping the latest entry
          const uniqueRegistrations = Array.from(new Map(result.data.map((reg: Registration) => [reg.documentId, reg])).values()) as Registration[];
          setRegistrations(uniqueRegistrations);
        } else {
          setRegistrations([]);
        }

        setLoading(false);
      } catch (err) {
        console.error('Fetch error:', err);
        setError(err instanceof Error ? err.message : 'Failed to fetch registrations');
        setLoading(false);
      }
    };

    fetchRegistrations();
  }, []);

  const groupedRegistrations = registrations.reduce((groups: GroupedRegistrations, reg) => {
    if (!groups[reg.EventName]) {
      groups[reg.EventName] = [];
    }
    groups[reg.EventName].push(reg);
    return groups;
  }, {});

  const toggleEvent = (eventName: string) => {
    setExpandedEvents(prev => {
      const newSet = new Set(prev);
      if (newSet.has(eventName)) {
        newSet.delete(eventName);
      } else {
        newSet.add(eventName);
      }
      return newSet;
    });
  };

  const formatDateAsText = (dateString: string) => {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return 'Invalid Date';
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const downloadCSV = () => {
    if (registrations.length === 0) {
      alert(t('adminContacts.noDataToDownload'));
      return;
    }

    const headers = [
      t('adminContacts.id' as TranslationKey),
      t('adminContacts.name'),
      t('adminContacts.email'),
      t('adminContacts.phone'),
      t('adminContacts.eventName'),
      t('adminContacts.organization'),
      t('adminContacts.submitted')
    ];

    const csvData = registrations.map(reg => [
      reg.id,
      reg.Name || '',
      reg.email || '',
      `="${reg.Number || ''}"`,
      reg.EventName || '',
      reg.OrganizationName || '',
      `="${formatDateAsText(reg.createdAt)}"` // fixed for Excel
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const BOM = '\uFEFF';
    const csvWithBOM = BOM + csvContent;

    const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `event-registrations-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const downloadEventCSV = (eventName: string, eventRegistrations: Registration[]) => {
    if (eventRegistrations.length === 0) {
      alert(t('adminContacts.noRegistrationsForEvent'));
      return;
    }

    const headers = [
      t('adminContacts.id' as TranslationKey),
      t('adminContacts.name'),
      t('adminContacts.email'),
      t('adminContacts.phone'),
      t('adminContacts.organization'),
      t('adminContacts.submitted')
    ];

    const csvData = eventRegistrations.map(reg => [
      reg.id,
      reg.Name || '',
      reg.email || '',
      `="${reg.Number || ''}"`,
      reg.OrganizationName || '',
      `="${formatDateAsText(reg.createdAt)}"` // fixed for Excel
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(field => `"${String(field).replace(/"/g, '""')}"`).join(','))
      .join('\n');

    const BOM = '\uFEFF';
    const csvWithBOM = BOM + csvContent;

    const blob = new Blob([csvWithBOM], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${eventName.replace(/[^a-zA-Z0-9\u4e00-\u9fff]/g, '_')}-registrations-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  // Add handler for status change
  const handleStatusChange = async (documentId: string, newStatus: 'Registered' | 'Cancelled' | 'Attended') => {
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_STRAPI_URL}/registrations/${documentId}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_STRAPI_TOKEN}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ data: { statusreg: newStatus } }),
      });
      if (!response.ok) throw new Error('Failed to update status');
      // Update local state
      setRegistrations(prev => prev.map(r => r.documentId === documentId ? { ...r, statusreg: newStatus } : r));
      toast.success(t('statusUpdateToast', { status: t(`statusreg.${newStatus}` as TranslationKey) }));
    } catch (err) {
      toast.error('Failed to update status.');
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#1E1B4B]"></div>
        <span className="ml-3 text-gray-700">
          <span className="block">{t('adminContacts.loading')}</span>
          <span className="text-sm text-gray-500">{t('adminContacts.loadingEn')}</span>
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="bg-red-50 p-6 rounded-lg shadow-sm border border-red-100 max-w-md">
          <div className="text-red-700 font-medium mb-2">{error}</div>
          <div className="text-red-600 text-sm mb-4">
            <span className="block">{t('adminContacts.error')}</span>
            <span className="block text-red-500 mt-1">{t('adminContacts.errorEn')}</span>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors w-full text-center"
          >
            {t('adminContacts.refresh')}
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-2 sm:px-4 py-8">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-4 mb-6 text-center sm:text-left">
        <h1 className="text-xl sm:text-2xl font-bold">{t('adminContacts.title')}</h1>
        <Button onClick={downloadCSV} className="flex items-center gap-2 w-full sm:w-auto justify-center">
          <Download className="h-5 w-5" />
          {t('adminContacts.downloadAll')}
        </Button>
      </div>
      {Object.keys(groupedRegistrations).length === 0 ? (
        <div className="text-center text-gray-500 py-12">{t('adminContacts.noDataToDownload')}</div>
      ) : (
        Object.entries(groupedRegistrations).map(([eventName, eventRegistrations]) => (
          <div key={eventName} className="mb-8 border rounded-lg shadow-sm">
            <div
              className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4 p-4 bg-[#1E1B4B]/5 cursor-pointer"
              onClick={() => toggleEvent(eventName)}
            >
              <div className="font-semibold text-base sm:text-lg text-center sm:text-left">{eventName}</div>
              <Button
                onClick={e => {
                  e.stopPropagation();
                  downloadEventCSV(eventName, eventRegistrations);
                }}
                className="flex items-center gap-2 w-full sm:w-auto justify-center"
              >
                <Download className="h-4 w-4" />
                {t('adminContacts.downloadEvent')}
              </Button>
            </div>
            {expandedEvents.has(eventName) && (
              <div className="overflow-x-auto">
                <table className="min-w-full divide-y divide-gray-200 border-collapse text-xs sm:text-sm">
                  <thead className="bg-[#1E1B4B]/5">
                    <tr>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t('adminContacts.id' as TranslationKey)}</th>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t('adminContacts.name')}</th>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t('adminContacts.email')}</th>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t('adminContacts.phone')}</th>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t('adminContacts.organization')}</th>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t("adminContacts.status")}</th>
                      <th className="px-2 sm:px-6 py-3 text-left text-xs font-medium text-[#1E1B4B] uppercase tracking-wider">{t('adminContacts.submitted')}</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {eventRegistrations.map(reg => (
                      <tr key={reg.id}>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">{reg.id}</td>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">{reg.Name}</td>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">{reg.email}</td>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">{reg.Number}</td>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">{reg.OrganizationName}</td>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">
                          <Select value={reg.statusreg || 'Registered'} onValueChange={val => handleStatusChange(reg.documentId, val as 'Registered' | 'Cancelled' | 'Attended')}>
                            <SelectTrigger className="w-[120px]">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Registered">Registered</SelectItem>
                              <SelectItem value="Cancelled">Cancelled</SelectItem>
                              <SelectItem value="Attended">Attended</SelectItem>
                            </SelectContent>
                          </Select>
                        </td>
                        <td className="px-2 sm:px-6 py-4 whitespace-nowrap text-xs sm:text-sm">{formatDateAsText(reg.createdAt)}</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </div>
        ))
      )}
    </div>
  );
}

export default function ContactSubmissionsPage() {
  return (
    <Suspense fallback={<div>Loading admin dashboard...</div>}>
      <ContactSubmissionsPageContent />
    </Suspense>
  );
}
