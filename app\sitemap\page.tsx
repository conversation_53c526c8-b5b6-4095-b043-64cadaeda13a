"use client"

import { useState, Suspense } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronRight, Home, Building2, Calendar, BookOpen, Phone, UserPlus, History, Newspaper } from "lucide-react"
import Link from "next/link"
import { routes, mainNavigation } from "@/app/routes"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

function SiteMapContent() {
  const [selectedSection, setSelectedSection] = useState("all")
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // Helper to get localized title
  const getLocalizedTitle = (titleObj: { en: string; zh: string; cn?: string }) => {
    if (currentLang === 'cn') {
      return titleObj.cn || titleObj.zh
    } else if (currentLang === 'zh') {
      return titleObj.zh
    }
    return titleObj.en
  }

  // Define the detailed event items as in the header dropdown, in the specified order
  const eventItems = [
    { key: "upcoming", label: t("navigation.upcomingEvents"), path: "/events/upcoming" },
    { key: "newEra", label: t("activities.newEra"), path: "/events/elite-programme" },
    { key: "elderlyActivities", label: t("activities.elderlyActivities"), path: "/events/elderly-activities" },
    { key: "youthFestival", label: t("activities.youthFestival"), path: "/events/youth-festival" },
    { key: "forums", label: t("activities.forums"), path: "/events/forums" },
    { key: "reception", label: t("navigation.reception"), path: "/events/reception" },
    { key: "monthlyMeetings", label: t("navigation.monthlyMeetings"), path: "/events/monthly-meetings" },
    { key: "exchangeVisits", label: t("navigation.exchangeVisits"), path: "/events/exchange-visits" },
    { key: "affiliatedActivities", label: t("navigation.affiliatedActivities"), path: "/events/affiliated-activities" },
    { key: "socialCharity", label: t("socialCharity.title"), path: "/events/social-charity" },
    { key: "careTeam", label: t("activities.careTeam"), path: "/events/care-team" },
    { key: "elderlyCenter", label: t("activities.elderlyCenter"), path: "/about-kcc/elderly-center" },
    { key: "seaScouts", label: t("activities.seaScouts"), path: "/events/sea-scouts" },
    { key: "otherActivities", label: t("navigation.otherActivities"), path: "/events/other-activities" },
    { key: "newsCenter", label: t("navigation.newsCenter"), path: "/news" },
  ]

  // Static site structure data - Following exact header navigation structure
  const siteStructure = [
    {
      title: { en: "Home", zh: "主頁" },
      path: "/",
      icon: <Home className="w-5 h-5" />,
      category: "main",
      items: [
        {
          title: { en: "Latest News", zh: "最新消息" },
          path: "/news",
          description: {
            en: "Stay updated with KCC's latest announcements and events",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          },
        },
        {
          title: { en: "What is KCC", zh: "商會簡介" },
          path: "/about-kcc/introduction",
          description: {
            en: "Learn about our organization and mission",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          },
        },
        {
          title: { en: "Management Team", zh: "管理團隊" },
          path: "/about-kcc/management",
          description: {
            en: "Meet our leadership team",
            zh: "認識我們的領導團隊",
            cn: "认识我们的领导团队"
          },
        },
      ],
    },
    {
      title: { en: "About KCC", zh: "關於本會" },
      path: "/about-kcc",
      icon: <Building2 className="w-5 h-5" />,
      category: "about",
      items: [
        {
          title: { en: "Our Chamber", zh: "商會簡介" },
          path: "/about-kcc/our-chamber",
          description: {
            en: "Learn about our organization and mission",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          },
        },
        {
          title: { en: "Chamber Org", zh: "商會架構" },
          path: "/about-kcc/chamber-org",
          description: {
            en: "Understanding our organizational framework",
            zh: "了解我們的組織架構與運作",
            cn: "了解我们的组织架构与运作"
          },
        },
        {
          title: { en: "Council Member + Past Councils", zh: "理監事會名單+歷屆理監事明表" },
          path: "#",
          description: {
            en: "Board members and historical council information",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          },
          type: "split",
          splitItems: [
            {
              title: { en: "Council Member", zh: "理監事會名單" },
              path: "/about-kcc/council-member",
              description: {
                en: "Current board members",
                zh: "現任理監事會成員名單",
                cn: "现任理监事会成员名单"
              },
            },
            {
              title: { en: "Past Councils", zh: "歷屆理監事明表" },
              path: "/history/councilors",
              description: {
                en: "Historical list of council members",
                zh: "歷屆理監事會成員名單",
                cn: "历届理监事会成员名单"
              },
            },
          ],
        },
        {
          title: { en: "Affiliated Association", zh: "屬會名單" },
          path: "/about-kcc/affiliated-associations",
          description: {
            en: "Our affiliated associations and partners",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          },
        },
        {
          title: { en: "KCC Development + Milestones", zh: "龍總發展史簡+本會發展里程" },
          path: "#",
          description: {
            en: "Our development history and key milestones",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          },
          type: "split",
          splitItems: [
            {
              title: { en: "KCC Development", zh: "龍總發展歷程" },
              path: "/about-kcc/development-history",
              description: {
                en: "KCC development history",
                zh: "龍總發展歷程",
                cn: "九龙总商会发展历程"
              },
            },
            {
              title: { en: "Our Milestones", zh: "本會重要里程碑" },
              path: "/history/milestones",
              description: {
                en: "Key milestones in our journey",
                zh: "本會重要里程碑",
                cn: "本会重要里程碑"
              },
            },
          ],
        },
      ],
    },
    {
      title: { en: "Events", zh: "活動項目" },
      path: "/events",
      icon: <Calendar className="w-5 h-5" />,
      category: "events",
      items: [
        {
          title: { en: "Upcoming Events", zh: "活動預告", cn: "活动预告" },
          path: "/events/upcoming",
          description: {
            en: "See what's coming up at KCC.",
            zh: "了解九龍總商會的使命、願景和價值觀",
            cn: "了解九龙总商会的使命、愿景和价值观"
          }
        },
        {
          title: { en: "Events Review", zh: "活動回顧", cn: "活动回顾" },
          path: "/events/events-review",
          description: {
            en: "Review of past and current events.",
            zh: "過去及現在活動回顧。",
            cn: "过去及现在活动回顾。"
          },
          type: "grid",
          layout: "2x2",
          gridItems: [
            {
              title: { en: "KCC Elite Development Program for the New Century", zh: "新紀元精英培訓計劃", cn: "新纪元精英培训计划" },
              path: "/events/new-era",
              description: {
                en: "Business innovation and leadership training for the new era.",
                zh: "新時代的商業創新與領袖培訓。",
                cn: "新时代的商业创新与领导力培训。"
              }
            },
            {
              title: { en: "Senior Citizens Activities", zh: "長者康樂活動", cn: "长者康乐活动" },
              path: "/events/elderly-activities",
              description: {
                en: "Activities for senior members to maintain an active social life.",
                zh: "為長者會員設計的康樂活動，維持活躍社交生活。",
                cn: "为长者会员设计的康乐活动，维持活跃社交生活。"
              }
            },
            {
              title: { en: "Youth Festival", zh: "青年節", cn: "青年节" },
              path: "/events/youth-festival",
              description: {
                en: "Youth festival activities to inspire young potential and cultivate future leaders.",
                zh: "激發青年潛能，培養未來領袖的青年節活動。",
                cn: "激发青年潜能，培养未来领袖的青年节活动。"
              }
            },
            {
              title: { en: "Forums", zh: "論壇", cn: "论坛" },
              path: "/events/forums",
              description: {
                en: "Business and community forums.",
                zh: "商業及社區論壇。",
                cn: "商业及社区论坛。"
              }
            },
          ],
        },
        {
          title: { en: "Reception Guests & Monthly Meeting", zh: "接待禮賓及例會", cn: "接待礼宾及例会" },
          path: "/events/reception",
          description: {
            en: "Reception guests and monthly meeting information",
            zh: "接待禮賓及例會相關資訊",
            cn: "接待礼宾及例会相关资讯"
          },
        },
        {
          title: { en: "Outbound Visits", zh: "外訪交流", cn: "外访交流" },
          path: "/events/exchange-visits",
          description: {
            en: "Exchange visits and international cooperation",
            zh: "外訪交流及國際合作",
            cn: "外访交流及国际合作"
          },
        },
        {
          title: { en: "Affiliated Activities", zh: "屬會活動" },
          path: "/events/affiliated-activities",
          description: {
            en: "Activities organized by affiliated associations",
            zh: "屬會舉辦的各項活動",
            cn: "属会举办的各项活动"
          },
        },
        {
          title: { en: "Social Charity Activities", zh: "社會慈善活動", cn: "社会慈善活动" },
          path: "/events/social-charity",
          description: {
            en: "Community welfare and social responsibility programs",
            zh: "社區福利和社會責任項目",
            cn: "社区福利和社会责任项目"
          },
          type: "grid",
          layout: "1x3",
          gridItems: [
            {
              title: { en: "Care Teams", zh: "關愛隊", cn: "关爱队" },
              path: "/events/social-charity/care-team",
              description: {
                en: "Community care initiatives",
                zh: "社區關愛行動",
                cn: "社区關愛行動"
              },
            },
            {
              title: { en: "Elderly Center", zh: "耆英中心", cn: "耆英中心" },
              path: "/about-kcc/elderly-center",
              description: {
                en: "Elderly center services",
                zh: "耆英中心服務",
                cn: "耆英中心服务"
              },
            },
            {
              title: { en: "Sea Scouts", zh: "海童軍", cn: "海童军" },
              path: "/events/sea-scouts",
              description: {
                en: "Sea scouts activities",
                zh: "海童軍活動",
                cn: "海童军活动"
              },
            },
          ],
        },
        {
          title: { en: "Other Activities", zh: "其它活動" },
          path: "/events/other-activities",
          description: {
            en: "Various other chamber activities",
            zh: "其他商會活動",
            cn: "其他商会活动"
          },
        },
        {
          title: { en: "News Center", zh: "新聞中心" },
          path: "/news",
          description: {
            en: "Latest news and announcements",
            zh: "最新消息及公告",
            cn: "最新消息及公告"
          },
        },
      ],
    },
    {
      title: { en: "Contact Us", zh: "聯絡我們" },
      path: "/contact",
      icon: <Phone className="w-5 h-5" />,
      category: "contact",
      items: [
        {
          title: { en: "Join Our Association", zh: "加入本會" },
          path: "/about-kcc/join-us",
          description: {
            en: "Information on how to become a member",
            zh: "了解如何成為本會會員",
            cn: "了解如何成为本会会员"
          },        },
        {
          title: { en: "Suggestion Box + Contact Us", zh: "意見箱+聯絡我們" },
          path: "#",
          description: {
            en: "Share your suggestions or contact us directly",
            zh: "歡迎提供寶貴意見或直接聯絡我們",
            cn: "欢迎提供宝贵意见或直接联系我们"
          },
          type: "split",
          splitItems: [
            {
              title: { en: "Suggestion Box", zh: "意見箱" },
              path: "/suggestion",
              description: {
                en: "Submit your suggestions and feedback",
                zh: "提交您的建議與反饋",
                cn: "提交您的建议与反馈"
              },
            },
            {
              title: { en: "Contact Us", zh: "聯絡我們" },
              path: "/contact",
              description: {
                en: "Get in touch with us",
                zh: "與我們聯絡",
                cn: "与我们联系"
              },
            },
          ],
        },
        {
          title: { en: "Rental Units", zh: "出租單位" },
          path: "/rental",
          description: {
            en: "Information about available rental units",
            zh: "可供出租單位的相關資訊",
            cn: "可供出租单位的相关信息"
          },
        },
      ],
    },
  ]

  const categories = [
    { id: "all", label: t('sitemap.allSections') },
    { id: "main", label: t('sitemap.main') },
    { id: "about", label: t('sitemap.about') },
    { id: "events", label: t('sitemap.events') },
    { id: "contact", label: t('sitemap.contact') },
  ]

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-muted/50">
      <div className="container py-12 space-y-8">
        {/* Hero Section */}
        <div className="relative overflow-hidden rounded-lg bg-primary text-primary-foreground p-8 md:p-12">
          <div className="relative z-10">
            <h1 className="text-3xl md:text-4xl font-bold mb-4">{t('navigation.sitemap' as any) || 'Sitemap'}</h1>
            <p className="text-primary-foreground/80 max-w-xl">
              {t('navigation.sitemapDescription')}
            </p>
          </div>
          <div className="absolute inset-0 bg-[url('/logo.jpeg')] opacity-10" />
        </div>

        {/* Category Filter */}
        <div className="flex overflow-auto pb-2 -mx-2 px-2">
          <div className="flex gap-2">
            {categories.map((category) => (
              <Button
                key={category.id}
                variant={selectedSection === category.id ? "default" : "outline"}
                onClick={() => setSelectedSection(category.id)}
                className="whitespace-nowrap"
              >
                {category.label}
              </Button>
            ))}
          </div>
        </div>

        {/* Main Content */}
        <div className="grid gap-6">
          {siteStructure
            .filter((section) => selectedSection === "all" || section.category === selectedSection)
            .map((section, index) => (
              <Card key={index} className="group hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="flex items-center gap-3 mb-6">
                    <div className="p-2 rounded-md bg-primary/10 text-primary">{section.icon}</div>
                    <h2 className="text-xl font-semibold">
                      <Link href={section.path} className="hover:text-primary transition-colors">
                        {getLocalizedTitle(section.title)}
                      </Link>
                    </h2>
                  </div>

                  {section.items && (
                    <div className="grid gap-4 pl-4">
                      {section.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="space-y-2">
                          {/* Main Item */}
                          <div className="group/item p-3 rounded-md hover:bg-muted transition-colors">
                            <div className="flex items-start gap-2">
                              <ChevronRight className="h-5 w-5 mt-0.5 text-muted-foreground group-hover/item:text-primary transition-colors" />
                              <div className="flex-1">
                                <Link href={item.path} className="font-medium group-hover/item:text-primary transition-colors">
                                  {getLocalizedTitle(item.title)}
                                </Link>
                                {('subtitle' in item) && item.subtitle && typeof item.subtitle === 'object' && 'en' in item.subtitle && 'zh' in item.subtitle ? (
                                  <p className="text-xs text-muted-foreground mt-0.5">
                                    {getLocalizedTitle(item.subtitle as { en: string; zh: string; cn?: string })}
                                  </p>
                                ) : null}
                                {item.description && (
                                  <p className="text-sm text-muted-foreground mt-1">
                                    {typeof item.description === 'object' && item.description && 'en' in item.description && 'zh' in item.description
                                      ? getLocalizedTitle(item.description as { en: string; zh: string; cn?: string })
                                      : item.description}
                                  </p>
                                )}
                              </div>
                            </div>
                          </div>

                          {/* Split Items */}
                          {item.type === "split" && Array.isArray(item.splitItems) && item.splitItems.length > 0 && (
                            <div className="ml-8 grid gap-2 md:grid-cols-2">
                              {item.splitItems.map((splitItem, splitIndex) => (
                                <Link
                                  key={splitIndex}
                                  href={splitItem.path}
                                  className="group/split p-2 rounded-md hover:bg-muted/50 transition-colors border border-border/50"
                                >
                                  <div className="flex items-start gap-2">
                                    <ChevronRight className="h-4 w-4 mt-0.5 text-muted-foreground group-hover/split:text-primary transition-colors" />
                                    <div>
                                      <span className="text-sm font-medium group-hover/split:text-primary transition-colors">
                                        {getLocalizedTitle(splitItem.title)}
                                      </span>
                                      {splitItem.description && (
                                        <p className="text-xs text-muted-foreground mt-0.5">
                                          {typeof splitItem.description === 'object' && splitItem.description && 'en' in splitItem.description && 'zh' in splitItem.description
                                            ? getLocalizedTitle(splitItem.description as { en: string; zh: string; cn?: string })
                                            : splitItem.description}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          )}

                          {/* Grid Items */}
                          {item.type === "grid" && 'gridItems' in item && Array.isArray(item.gridItems) && item.gridItems.length > 0 && (
                            <div className={`ml-8 grid gap-2 ${
                              'layout' in item && item.layout === "2x2" ? "grid-cols-1 md:grid-cols-2" :
                              'layout' in item && item.layout === "1x3" ? "grid-cols-1 md:grid-cols-3" :
                              "grid-cols-1"
                            }`}>
                              {item.gridItems.map((gridItem: { title: { en: string; zh: string; cn?: string }; path: string; description: string | { en: string; zh: string; cn?: string } }, gridIndex: number) => (
                                <Link
                                  key={gridIndex}
                                  href={gridItem.path}
                                  className="group/grid p-2 rounded-md hover:bg-muted/50 transition-colors border border-border/50"
                                >
                                  <div className="flex items-start gap-2">
                                    <ChevronRight className="h-4 w-4 mt-0.5 text-muted-foreground group-hover/grid:text-primary transition-colors" />
                                    <div>
                                      <span className="text-sm font-medium group-hover/grid:text-primary transition-colors">
                                        {getLocalizedTitle(gridItem.title)}
                                      </span>
                                      {gridItem.description && (
                                        <p className="text-xs text-muted-foreground mt-0.5">
                                          {typeof gridItem.description === 'object' && gridItem.description && 'en' in gridItem.description && 'zh' in gridItem.description
                                            ? getLocalizedTitle(gridItem.description as { en: string; zh: string; cn?: string })
                                            : gridItem.description}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                </Link>
                              ))}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
        </div>
      </div>
    </div>
  )
}

export default function SiteMap() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SiteMapContent />
    </Suspense>
  )
}

