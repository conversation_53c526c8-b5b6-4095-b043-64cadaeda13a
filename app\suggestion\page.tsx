"use client"

import { useState, useMemo, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import { format } from "date-fns"

import AnimatedHero from "@/components/animated-hero"
import { SuggestionFormData, submitSuggestion } from "@/lib/strapi"
import { toast } from "sonner"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"

// Loading component
function SuggestionLoading() {
  return (
    <div className="container py-12">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <div className="h-6 bg-gray-200 rounded animate-pulse"></div>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array(8).fill(null).map((_, index) => (
              <div key={index} className="h-10 bg-gray-200 rounded animate-pulse"></div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Main content component
function SuggestionContent() {
  const searchParams = useSearchParams()
  const [formData, setFormData] = useState({
    title: "",
    message: "",
    category: "",
    name: "",
    email: "",
    date: format(new Date(), "yyyy-MM-dd")
  })
  const [submitting, setSubmitting] = useState(false)

  // Get current language and translation function - memoize to prevent re-renders
  const currentLang = useMemo(() => getLanguageFromParams(searchParams), [searchParams])
  const { t } = useTranslation(currentLang)

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setSubmitting(true)

    try {
      await submitSuggestion(formData as SuggestionFormData, currentLang)

      // Reset form
      setFormData({
        title: "",
        message: "",
        category: "",
        name: "",
        email: "",
        date: format(new Date(), "yyyy-MM-dd")
      })

      toast.success(t('suggestionBox.submitSuccess'))
    } catch (err) {
      console.error("Failed to submit suggestion:", err)
      toast.error(t('suggestionBox.submitError'))
    } finally {
      setSubmitting(false)
    }
  }

  return (
    <div className="container py-12">
      {/* Centered Suggestion Form */}
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle>{t('suggestionBox.formTitle')}</CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="title">
                  {t('suggestionBox.suggestionTitle')} <span className="text-red-500">{t('suggestionBox.required')}</span>
                </Label>
                <Input
                  id="title"
                  name="title"
                  value={formData.title}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">
                  {t('suggestionBox.suggestionMessage')} <span className="text-red-500">{t('suggestionBox.required')}</span>
                </Label>
                <Textarea
                  id="message"
                  name="message"
                  value={formData.message}
                  onChange={handleInputChange}
                  rows={5}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">
                  {t('suggestionBox.category')} <span className="text-red-500">{t('suggestionBox.required')}</span>
                </Label>
                <Input
                  id="category"
                  name="category"
                  value={formData.category}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="name">
                  {t('suggestionBox.name')} <span className="text-red-500">{t('suggestionBox.required')}</span>
                </Label>
                <Input
                  id="name"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  {t('suggestionBox.email')} <span className="text-red-500">{t('suggestionBox.required')}</span>
                </Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-[#1E1B4B] hover:bg-[#1E1B4B]/90"
                disabled={submitting}
              >
                {submitting ? t('suggestionBox.submitting') : t('suggestionBox.submit')}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Component that uses searchParams - wrapped in Suspense
function SuggestionPageContent() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)

  return (
    <div className="min-h-screen">
      <AnimatedHero
        title="suggestionBox.title"
        description="suggestionBox.description"
        image="/logo.jpeg"
        lang={currentLang}
      />

      <Suspense fallback={<SuggestionLoading />}>
        <SuggestionContent />
      </Suspense>
    </div>
  )
}

// Main page component that wraps everything in Suspense
export default function SuggestionPage() {
  return (
    <Suspense fallback={<SuggestionLoading />}>
      <SuggestionPageContent />
    </Suspense>
  )
}