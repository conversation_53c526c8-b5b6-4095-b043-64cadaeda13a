"use client"

import { motion } from "framer-motion"
import Image from "next/image"
import { getTranslation, type Language, type TranslationKey } from "@/lib/translations"

interface AnimatedHeroProps {
  title?: {
    zh: string // Traditional Chinese
    cn?: string // Simplified Chinese (optional, falls back to zh if not provided)
    en: string
  } | TranslationKey // Can also use translation key
  description?: {
    zh: string // Traditional Chinese
    cn?: string // Simplified Chinese (optional, falls back to zh if not provided)
    en: string
  } | TranslationKey // Can also use translation key
  image?: string
  height?: "small" | "medium" | "large"
  lang?: string // Language parameter to control which language to display
}

export default function AnimatedHero({
  title = {
    zh: "九龍總商會",
    en: "Kowloon Chamber of Commerce",
  },
  description,
  image = "/logo.jpeg",
  height = "medium",
  lang = "en", // Default to English
}: AnimatedHeroProps) {
  // Helper function to get the appropriate text based on language
  const getLocalizedText = (textObj: { zh: string; cn?: string; en: string } | TranslationKey | undefined) => {
    if (!textObj) return ""

    // If it's a translation key (string), use the translation system
    if (typeof textObj === 'string') {
      return getTranslation(textObj, lang as Language)
    }

    // If it's a legacy object, use the old logic
    if (lang === "zh") {
      return textObj.zh // Traditional Chinese
    } else if (lang === "cn") {
      return textObj.cn || textObj.zh // Simplified Chinese, fallback to Traditional
    }
    return textObj.en // English (default)
  }

  // Get title text for alt attribute
  const getTitleText = () => {
    if (typeof title === 'string') {
      return getTranslation(title, 'en' as Language)
    }
    return title?.en || "Kowloon Chamber of Commerce"
  }

  const heightClasses = {
    small: "h-[30vh]",
    medium: "h-[40vh]",
    large: "h-[60vh]",
  }

  return (
    <section className={`relative flex items-center justify-center bg-[#1E1B4B] ${heightClasses[height]}`}>
      <motion.div
        className="absolute inset-0"
        initial={{ scale: 1.2, opacity: 0 }}
        animate={{ scale: 1, opacity: 0.2 }}
        transition={{ duration: 1.5, ease: "easeOut" }}
      >
        <Image src={image || "/logo.jpeg"} alt={getTitleText()} fill className="object-cover" priority />
      </motion.div>

      <div className="absolute inset-0 bg-gradient-to-b from-[#1E1B4B]/50 to-[#1E1B4B]/80" />

      <div className="relative text-center px-4">
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.8 }}
        >
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {getLocalizedText(title)}
          </h1>
          {description && (
            <motion.p
              className="text-xl text-white/80 max-w-2xl mx-auto"
              initial={{ y: 20, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              transition={{ delay: 0.8, duration: 0.8 }}
            >
              {getLocalizedText(description)}
            </motion.p>
          )}
        </motion.div>
      </div>

      <motion.div
        className="absolute bottom-0 left-0 right-0 h-16 bg-gradient-to-t from-background to-transparent"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 1, duration: 1 }}
      />
    </section>
  )
}

