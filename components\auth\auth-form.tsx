"use client"

import { useState } from "react"
import { toast } from "sonner"
import { useForm, FieldErrors } from "react-hook-form"
import { useRouter, useSearchParams } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Icons } from "@/components/ui/icons"
import { fetchAPI } from "@/strapi"
import { useUser } from "@/contexts/user-context"
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { useTranslation, getLanguageFromParams, TranslationKey } from "@/lib/translations"

// Define form data types
interface BaseFormData {
  mode: "login" | "register";
  password: string;
}

interface LoginFormData extends BaseFormData {
  mode: "login";
  identifier: string;
}

interface RegisterFormData extends BaseFormData {
  mode: "register";
  username: string;
  email: string;
}

type AuthFormData = LoginFormData | RegisterFormData;

interface AuthFormProps {
  mode: "login" | "register"
}

export function AuthForm({ mode }: AuthFormProps) {
  const { setUser } = useUser()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [uiError, setUiError] = useState<string | null>(null)

  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  // Validation schemas
  const loginSchema = z.object({
    mode: z.literal("login"),
    identifier: z.string().min(3, t("login.error.requiredUsernameOrEmail")),
    password: z.string().min(8, t("login.error.requiredPassword")),
  });

  const registerSchema = z.object({
    mode: z.literal("register"),
    username: z.string().min(3, t("register.error.requiredUsername")),
    email: z.string().email(t("register.error.invalidEmail")),
    password: z.string().min(8, t("register.error.requiredPassword")),
  });

  const authSchema = z.discriminatedUnion("mode", [loginSchema, registerSchema]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<AuthFormData>({
    resolver: zodResolver(authSchema),
    defaultValues: {
      mode
    }
  })

  const router = useRouter()

  async function onSubmit(formData: AuthFormData) {
   setIsLoading(true)
   setUiError(null)

   try {
     const endpoint = mode === "register" ? "auth/local/register" : "auth/local"
     const body = mode === "register"
       ? {
           username: (formData as RegisterFormData).username,
           email: (formData as RegisterFormData).email,
           password: formData.password
         }
       : {
           identifier: (formData as LoginFormData).identifier,
           password: formData.password
         }

     const response = await fetchAPI(endpoint, {}, {
       method: "POST",
       body: JSON.stringify(body)
     })

      // Handle expected auth errors (400/401)
      if (response && response.error) {
        const errorMessage = response.error?.error?.message || response.error?.message || 'Unknown error';
        if (mode === "login") {
          if (errorMessage.toLowerCase().includes('password')) {
            setUiError(t("login.error.passwordWrong"));
          } else {
            setUiError(t("login.error.invalidCredentials"));
          }
        } else if (mode === "register") {
          if (errorMessage.toLowerCase().includes('email')) {
            toast.error(t("register.error.emailTaken"));
          } else if (errorMessage.toLowerCase().includes('username')) {
            toast.error(t("register.error.usernameTaken"));
          } else {
            toast.error(`Registration failed: ${errorMessage}`);
          }
        }
        setIsLoading(false);
        return;
      }

      if (response.jwt) {
        if (mode === "register") {
          toast.success(t("register.successMessage"))
          router.push(`/login?lang=${currentLang}`)
        } else {
          localStorage.setItem("token", response.jwt)
          setUser(response.user)
          toast.success(t("login.button"))
          const returnUrl = searchParams.get("returnUrl") || `/dashboard?lang=${currentLang}`

          // If returning to registration page, add user email to URL and ensure lang param is present
          if (returnUrl.includes('/events/register')) {
            const url = new URL(returnUrl, window.location.origin);
            url.searchParams.set('email', encodeURIComponent(response.user.email));
            if (!url.searchParams.get('lang')) {
              url.searchParams.set('lang', currentLang);
            }
            router.push(url.pathname + url.search);
          } else {
            router.push(returnUrl)
          }
        }
      }
    } catch (error: any) {
      // Only handle unexpected errors here
      let errorMessage = error?.message || 'Unknown error';
      if (error?.response) {
        errorMessage = error.response?.data?.error?.message || error.response?.statusText || errorMessage;
      }
      setUiError(errorMessage);
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="flex min-h-screen items-center justify-center bg-gradient-to-br from-gray-50 to-blue-50">
      <Card className="w-full max-w-md border-0 shadow-lg rounded-2xl p-0">
        <CardHeader className="space-y-3 pb-6 text-center">
          <CardTitle className="text-3xl font-bold text-[#1E1B4B]">
            {mode === "login" ? t("login.title") : t("register.title")}
          </CardTitle>
          <CardDescription className="text-base text-gray-600">
            {mode === "login" ? t("login.subtitle") : t("register.subtitle")}
          </CardDescription>
        </CardHeader>

        {uiError && (
          <div className="mx-6 mb-4 p-3 bg-red-100 text-red-700 rounded text-center text-sm">
            {uiError}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)}>
          <CardContent className="grid gap-6 px-6">
            {mode === "register" && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="username" className="text-[#1E1B4B] font-medium">
                    {t("register.username")}
                  </Label>
                  <Input
                    id="username"
                    type="text"
                    placeholder={t("register.usernamePlaceholder")}
                    className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition"
                    {...register("username")}
                  />
                  {mode === "register" && (errors as FieldErrors<RegisterFormData>).username && (
                    <p className="text-sm text-red-500">{(errors as FieldErrors<RegisterFormData>).username?.message}</p>
                  )}
                </div>
                <div className="grid gap-2">
                  <Label htmlFor="email" className="text-[#1E1B4B] font-medium">
                    {t("register.email")}
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    placeholder={t("register.emailPlaceholder")}
                    className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition"
                    {...register("email")}
                  />
                  {mode === "register" && (errors as FieldErrors<RegisterFormData>).email && (
                    <p className="text-sm text-red-500">{(errors as FieldErrors<RegisterFormData>).email?.message}</p>
                  )}
                </div>
              </>
            )}
            {mode === "login" && (
              <>
                <div className="grid gap-2">
                  <Label htmlFor="identifier" className="text-[#1E1B4B] font-medium">
                    {t("login.usernameOrEmail")}
                  </Label>
                  <Input
                    id="identifier"
                    type="text"
                    placeholder={t("login.usernameOrEmailPlaceholder")}
                    className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition"
                    {...register("identifier")}
                  />
                  {mode === "login" && (errors as FieldErrors<LoginFormData>).identifier && (
                    <p className="text-sm text-red-500">{(errors as FieldErrors<LoginFormData>).identifier?.message}</p>
                  )}
                </div>
              </>
            )}
            <div className="grid gap-2">
              <Label htmlFor="password" className="text-[#1E1B4B] font-medium">
                {mode === "register" ? t("register.password") : t("login.password")}
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  placeholder={mode === "register" ? t("register.passwordPlaceholder") : t("login.passwordPlaceholder")}
                  autoComplete="current-password"
                  className="rounded-lg border-gray-300 focus:border-[#1E1B4B] focus:ring-2 focus:ring-[#1E1B4B]/30 transition pr-10"
                  {...register("password")}
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600"
                >
                  {showPassword ? <Icons.eyeOff className="h-5 w-5" /> : <Icons.eye className="h-5 w-5" />}
                </button>
              </div>
              {errors.password && (
                <p className="text-sm text-red-500">{errors.password.message}</p>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex flex-col items-center gap-4 px-6 pb-6">
            <Button type="submit" disabled={isLoading} className="w-full bg-[#1E1B4B] hover:bg-[#1E1B4B]/90 text-white rounded-lg py-3 text-lg font-semibold transition-transform transform hover:scale-105">
              {isLoading ? (
                <Icons.spinner className="mr-2 h-4 w-4 animate-spin" />
              ) : (
                t(mode === "login" ? "login.button" : "register.button")
              )}
            </Button>
            {mode === "login" && (
              <a
                href={`/forgot-password?lang=${currentLang}`}
                className="text-sm text-gray-600 hover:text-[#1E1B4B] hover:underline"
              >
                {t("login.forgotPassword" as TranslationKey)}
              </a>
            )}
          </CardFooter>
        </form>
      </Card>
    </div>
  )
}