"use client"

import SingleLanguage<PERSON><PERSON><PERSON><PERSON> from "@/components/single-language-fancy-card"
import SingleLanguageSectionHeader from "@/components/single-language-section-header"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams, type TranslationKey } from "@/lib/translations"
import { fetchFeaturedEventPrograms, type FeaturedEventProgram } from "@/lib/strapi"
import Link from "next/link"
import { useEffect, useState } from "react"
import { Skeleton } from "@/components/ui/skeleton"

export default function ChamberEventFeatures() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  const [eventPrograms, setEventPrograms] = useState<FeaturedEventProgram[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Map language codes to Strapi locales
  const getStrapiLocale = () => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return 'en' // Default to English
    }
  }

  useEffect(() => {
    const loadEventPrograms = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetchFeaturedEventPrograms({
          populate: "*",
          locale: getStrapiLocale(),
          pagination: { pageSize: 10 }
        })

        setEventPrograms(response)
      } catch (error) {
        console.error("Error fetching featured event programs:", error)
        setError("Failed to load event programs")
      } finally {
        setLoading(false)
      }
    }

    loadEventPrograms()
  }, [currentLang]) // Re-fetch when language changes

  // Loading skeleton
  if (loading) {
    return (
      <section className="container py-16 md:py-24">
        <div className="text-center mb-12">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex flex-col h-full">
              <div className="flex flex-col flex-1 h-full justify-between p-6 border rounded-xl bg-white shadow-sm">
                <div>
                  <Skeleton className="h-6 w-full mb-2" />
                  <Skeleton className="h-4 w-full mb-2" />
                  <Skeleton className="h-4 w-3/4 mb-6" />
                </div>
                <Skeleton className="h-10 w-full" />
              </div>
            </div>
          ))}
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="container py-16 md:py-24">
        <SingleLanguageSectionHeader
          title={t('eventFeatures.title' as TranslationKey)}
          description={t('eventFeatures.description' as TranslationKey)}
          align="center"
        />
        <div className="text-center text-red-500 mt-8">
          <p>{error}</p>
        </div>
      </section>
    )
  }

  return (
    <section className="container py-16 md:py-24">
      <SingleLanguageSectionHeader
        title={t('eventFeatures.title' as TranslationKey)}
        description={t('eventFeatures.description' as TranslationKey)}
        align="center"
      />

      <div className="grid md:grid-cols-3 gap-8">
        {eventPrograms.map((program) => (
          <div key={program.id} className="flex flex-col h-full">
            <div className="flex flex-col flex-1 h-full justify-between p-6 border rounded-xl bg-white shadow-sm">
              <div>
                <h3 className="text-xl font-bold mb-2">{program.title}</h3>
                <p className="text-muted-foreground mb-6">{program.paragraph}</p>
              </div>
              <Link
                href={program.route}
                className="mt-auto inline-block w-full bg-primary text-white px-4 py-2 rounded hover:bg-primary/90 text-center font-medium"
              >
                {t('common.readMore')}
              </Link>
            </div>
          </div>
        ))}
      </div>

      {/* Show message if no programs available */}
      {eventPrograms.length === 0 && !loading && (
        <div className="text-center text-muted-foreground mt-8">
          <p>{t('common.noData' as TranslationKey)}</p>
        </div>
      )}
    </section>
  )
}