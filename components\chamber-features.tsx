"use client"

import { Building2, Globe2, Users } from "lucide-react"
import SingleLanguageFancy<PERSON>ard from "@/components/single-language-fancy-card"
import SingleLanguageSectionHeader from "@/components/single-language-section-header"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams, type TranslationKey } from "@/lib/translations"
import { fetchChamberFeatures, type ChamberFeature } from "@/lib/strapi"
import { useEffect, useState } from "react"
import { Skeleton } from "@/components/ui/skeleton"

// Icon mapping for different features
const getFeatureIcon = (index: number) => {
  const icons = [
    <Building2 key="building" className="w-6 h-6" />,
    <Globe2 key="globe" className="w-6 h-6" />,
    <Users key="users" className="w-6 h-6" />,
  ]
  return icons[index % icons.length] || <Building2 className="w-6 h-6" />
}

export default function ChamberFeatures() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  const [chamberFeatures, setChamberFeatures] = useState<ChamberFeature[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Map language codes to Strapi locales
  const getStrapiLocale = () => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return 'en' // Default to English
    }
  }

  useEffect(() => {
    const loadChamberFeatures = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetchChamberFeatures({
          populate: "*",
          locale: getStrapiLocale(),
          pagination: { pageSize: 10 }
        })

        setChamberFeatures(response)
      } catch (error) {
        console.error("Error fetching chamber features:", error)
        setError("Failed to load chamber features")
      } finally {
        setLoading(false)
      }
    }

    loadChamberFeatures()
  }, [currentLang]) // Re-fetch when language changes

  // Loading skeleton
  if (loading) {
    return (
      <section className="container py-16 md:py-24">
        <div className="text-center mb-12">
          <Skeleton className="h-8 w-64 mx-auto mb-4" />
          <Skeleton className="h-4 w-96 mx-auto" />
        </div>
        <div className="grid md:grid-cols-3 gap-8">
          {[1, 2, 3].map((i) => (
            <div key={i} className="flex flex-col h-full min-h-[200px]">
              <div className="p-6 border rounded-xl bg-white shadow-sm flex flex-col h-full">
                <div className="w-12 h-12 rounded-lg bg-gray-200 mb-4">
                  <Skeleton className="w-full h-full rounded-lg" />
                </div>
                <Skeleton className="h-6 w-full mb-2" />
                <Skeleton className="h-4 w-full mb-1" />
                <Skeleton className="h-4 w-3/4 flex-1" />
              </div>
            </div>
          ))}
        </div>
      </section>
    )
  }

  // Error state
  if (error) {
    return (
      <section className="container py-16 md:py-24">
        <SingleLanguageSectionHeader
          title={t('features.title' as TranslationKey)}
          description={t('features.description' as TranslationKey)}
          align="center"
        />
        <div className="text-center text-red-500 mt-8">
          <p>{error}</p>
        </div>
      </section>
    )
  }

  return (
    <section className="container py-16 md:py-24">
      <SingleLanguageSectionHeader
        title={t('features.title' as TranslationKey)}
        description={t('features.description' as TranslationKey)}
        align="center"
      />

      <div className="grid md:grid-cols-3 gap-8">
        {chamberFeatures.map((feature, i) => (
          <div key={feature.id} className="flex h-full min-h-[200px]">
            <SingleLanguageFancyCard
              icon={getFeatureIcon(i)}
              title={feature.title}
              description={feature.description}
              className="flex-1 h-full"
            />
          </div>
        ))}
      </div>

      {/* Show message if no features available */}
      {chamberFeatures.length === 0 && !loading && (
        <div className="text-center text-muted-foreground mt-8">
          <p>{t('common.noData' as TranslationKey)}</p>
        </div>
      )}
    </section>
  )
}
