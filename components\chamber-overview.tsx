"use client"

import Image from "next/image"
import { Card, CardContent } from "@/components/ui/card"
import Link from "next/link"
import { useEffect, useState } from "react"
import { fetchNews, fetchUpcomingEvents, fetchWhatIsKCCs, NewsItem as StrapiNewsItem } from "@/lib/strapi"
import { useSearchParams } from "next/navigation"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import GoogleDriveEmbed from "@/components/ui/google-drive-embed"

// Define the interfaces for the expected data structures
interface UpcomingEvent {
  id: number
  title: string
  date?: string
}

// Use the NewsItem interface from strapi.ts
type NewsItem = StrapiNewsItem;

// Helper function to check if a link is a Google Drive link
function isGoogleDriveLink(link: string): boolean {
  if (!link) return false;
  return link.includes('drive.google.com') || link.includes('docs.google.com');
}

export default function ChamberOverview() {
  const searchParams = useSearchParams()
  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  const [activities, setActivities] = useState<UpcomingEvent[]>([])
  const [activitiesLoading, setActivitiesLoading] = useState(true)
  const [newsItems, setNewsItems] = useState<NewsItem[]>([])
  const [newsLoading, setNewsLoading] = useState(true)
  const [whatIsKCC, setWhatIsKCC] = useState<Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }> | null>(null)
  const [whatIsKCCImage, setWhatIsKCCImage] = useState<any>(null)
  const [whatIsKCCLoading, setWhatIsKCCLoading] = useState(true)

  // Helper function to get Strapi locale from current language
  const getStrapiLocale = () => {
    switch (currentLang) {
      case 'zh':
        return 'zh-Hant-HK' // Traditional Chinese
      case 'cn':
        return 'zh-Hans-HK' // Simplified Chinese
      case 'en':
        return 'en' // English
      default:
        return undefined // Use default locale
    }
  }

  // Helper function to create a preview of the content
  const createContentPreview = (content: Array<{
    type: string
    children: Array<{
      text: string
      type: string
    }>
  }>) => {
    const fullText = content
      .map(paragraph =>
        paragraph.children?.map(child => child.text).join(' ')
      )
      .join(' ')

    // Limit to approximately 1300 characters and end at a complete sentence
    const maxLength = 1300
    if (fullText.length <= maxLength) {
      return fullText
    }

    const truncated = fullText.substring(0, maxLength)
    const lastSentenceEnd = Math.max(
      truncated.lastIndexOf('.'),
      truncated.lastIndexOf('。'), // Chinese period
      truncated.lastIndexOf('!'),
      truncated.lastIndexOf('？') // Chinese question mark
    )

    if (lastSentenceEnd > 650) {
      return truncated.substring(0, lastSentenceEnd + 1)
    }

    // If no sentence ending found, cut at last space
    const lastSpace = truncated.lastIndexOf(' ')
    return lastSpace > 650 ? truncated.substring(0, lastSpace) + '...' : truncated + '...'
  }

  useEffect(() => {
    const loadWhatIsKCC = async () => {
      try {
        const response = await fetchWhatIsKCCs({
          populate: "*",
          locale: getStrapiLocale()
        })

        // Type assertion for what is KCC data
        const whatIsKCCData = response?.data as any;

        if (whatIsKCCData && whatIsKCCData.length > 0) {
          const firstItem = whatIsKCCData[0];
          if (firstItem?.whatiskccframe) {
            // Parse the whatiskccframe content into the expected format
            const content = [{
              type: "paragraph",
              children: [{
                text: firstItem.whatiskccframe,
                type: "text"
              }]
            }];
            setWhatIsKCC(content)
          }
          if (firstItem?.image) {
            setWhatIsKCCImage(firstItem.image[0])
          }
        }
      } catch (error) {
        console.error("Error fetching What is KCC:", error)
      } finally {
        setWhatIsKCCLoading(false)
      }
    }

    loadWhatIsKCC()
  }, [currentLang])

  useEffect(() => {
    const loadActivities = async () => {
      try {
        

        const response = await fetchUpcomingEvents({
          populate: "*",
          pagination: {
            pageSize: 3
          },
          sort: "date:asc", // Changed to ascending for upcoming events (earliest first)
          locale: getStrapiLocale()
        })

       

        if (response?.data) {
          // Type assertion for upcoming events
          setActivities(response.data as any as UpcomingEvent[])
        } else {
          
          setActivities([])
        }
      } catch (error) {
        console.error("Error fetching activities:", error)
        setActivities([])
      } finally {
        setActivitiesLoading(false)
      }
    }

    loadActivities()
  }, [currentLang])

  useEffect(() => {
    const loadNews = async () => {
      try {
       

        // Calculate date from 4 weeks ago
        const fourWeeksAgo = new Date();
        fourWeeksAgo.setDate(fourWeeksAgo.getDate() - 28); // 4 weeks = 28 days

        // First try to fetch news with locale and date filter
        let response = await fetchNews({
          populate: "*",
          pagination: {
            pageSize: 3
          },
          sort: "date:desc",
          locale: getStrapiLocale(),
          filters: {
            date: {
              $gte: fourWeeksAgo.toISOString()
            }
          }
        });

        

        // If no results with date filter, try without date filter
        if (!response?.data || response.data.length === 0) {
        
          response = await fetchNews({
            populate: "*",
            pagination: {
              pageSize: 3
            },
            sort: "date:desc",
            locale: getStrapiLocale()
          });
          
        }

        if (response?.data) {
          setNewsItems(response.data as NewsItem[])
        }
      } catch (error) {
        console.error("Error fetching news:", error)
      } finally {
        setNewsLoading(false)
      }
    }

    loadNews()
  }, [currentLang])

  return (
    <div className="py-12 px-4 max-w-7xl mx-auto">
      <div className="grid grid-cols-1 md:grid-cols-10 gap-6">
        {/* Left Column - Full height News section (40% width) */}
        <div className="h-full md:col-span-4">
          {/* Latest News - full height */}
          <Card className="h-full">
            <CardContent className="p-6 h-full flex flex-col">
              <div className="flex items-center mb-4">
                <h2 className="text-xl font-bold">{t('chamber.latestNews')}</h2>
                <Link href="/news" className="ml-1">
                  <span className="text-xl">›</span>
                </Link>
              </div>

              <div className="space-y-4 flex-grow">
                {newsLoading ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="relative aspect-video bg-gray-200 mb-2" />
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 rounded w-3/4" />
                          <div className="h-3 bg-gray-200 rounded w-1/2" />
                        </div>
                      </div>
                    ))}
                  </div>
                ) : newsItems.length > 0 ? (
                  newsItems.map((item) => {
                    // Check for drivelink first (priority)
                    const drivelink = item.drivelink;
                    const hasDriveLink = isGoogleDriveLink(drivelink);

                    // Get the first media item as fallback
                    let media = item.image && item.image.length > 0 ? item.image[0] : null;
                    let mime = (media && 'mime' in media) ? (media as any).mime : (media?.url && media.url.endsWith('.mp4') ? 'video/mp4' : undefined);
                    let previewUrl = (media && 'previewUrl' in media) ? (media as any).previewUrl : undefined;

                 

                    return (
                      <div key={item.id} className="space-y-2">
                        <div className="relative h-[250px] bg-gray-100 flex items-center justify-center mb-2 overflow-hidden rounded-lg">
                          {hasDriveLink ? (
                            // Show Google Drive embed if drivelink exists
                            <GoogleDriveEmbed
                              videoId={drivelink}
                              title={item.title}
                              className="w-full h-[250px] rounded-lg"
                            />
                          ) : media && mime?.startsWith('video/') ? (
                            // Show video if no drivelink but video media exists
                            <video
                              src={media.url}
                              poster={previewUrl}
                              controls
                              className="object-cover w-full h-[250px] rounded-lg"
                              playsInline
                            />
                          ) : (
                            // Show image as final fallback
                            <Image
                              src={
                                media
                                  ? (media.formats?.medium?.url ||
                                     media.formats?.small?.url ||
                                     media.url ||
                                     "/logo.jpeg?height=250&width=400")
                                  : "/logo.jpeg?height=250&width=400"
                              }
                              alt={item.title}
                              width={400}
                              height={250}
                              className="object-cover w-full h-[250px] transition-transform hover:scale-105 duration-300"
                            />
                          )}
                          <div className="absolute top-2 right-2 bg-primary text-white text-xs px-2 py-1 rounded">
                            {t('chamber.latest')}
                          </div>
                        </div>
                        <div className="flex flex-col">
                          <span className="font-medium line-clamp-2">{item.title}</span>
                          <span className="text-sm text-primary">{new Date(item.date || item.createdAt || Date.now()).toLocaleDateString()}</span>
                        </div>
                      </div>
                    );
                  })
                ) : (
                  <div className="text-center text-gray-500">{t('chamber.noNewsAvailable')}</div>
                )}
              </div>

              <div className="mt-auto pt-6 text-center">
                <Link href="/news" className="block w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary/90">
                  {t('chamber.viewMoreNews')}
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Column - What is KCC and Events sections (60% width) */}
        <div className="flex flex-col gap-6 h-full md:col-span-6">
          {/* Chamber Introduction - top section (70% height) */}
          <Card className="flex-[0.7]">
            <CardContent className="p-6 flex flex-col h-full">
              <div className="flex items-center mb-4">
                <h2 className="text-xl font-bold">{t('chamber.whatIsKCC')}</h2>
                <Link href="/about-kcc/introduction" className="ml-1">
                  <span className="text-xl">›</span>
                </Link>
              </div>

              <div className="flex flex-col md:flex-row gap-6 flex-grow">
                <div className="space-y-4 flex-1">
                  {whatIsKCCLoading ? (
                    <div className="animate-pulse space-y-2">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-200 rounded w-1/2"></div>
                    </div>
                  ) : whatIsKCC ? (
                    <>
                      <div className="text-gray-700">
                        <p className="leading-relaxed">
                          {createContentPreview(whatIsKCC)}
                        </p>
                      </div>
                      <div className="flex mt-4">
                        <Link href="/about-kcc/introduction" className="bg-primary text-white px-4 py-2 rounded-md text-sm hover:bg-primary/90">{t('chamber.learnMore')}</Link>
                      </div>
                    </>
                  ) : (
                    <p className="text-gray-500">No content available</p>
                  )}
                </div>

                <div className="w-full md:w-2/5 flex justify-center">
                  <Image
                    src={whatIsKCCImage?.formats?.medium?.url || whatIsKCCImage?.url || "/logo.jpeg?height=400&width=400"}
                    alt="商會簡介圖片"
                    width={400}
                    height={400}
                    className="object-cover rounded-lg h-auto max-h-[400px]"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Chamber Activities - bottom section (30% height) */}
          <Card className="flex-[0.3]">
            <CardContent className="p-6 flex flex-col">
              <div className="flex items-center mb-4">
                <h2 className="text-xl font-bold">{t('chamber.events')}</h2>
                <Link href="/events/upcoming" className="ml-1">
                  <span className="text-xl">›</span>
                </Link>
              </div>

              <div className="space-y-2">
                {activitiesLoading ? (
                  <div className="space-y-4">
                    {[1, 2, 3].map((i) => (
                      <div key={i} className="animate-pulse">
                        <div className="h-3 bg-gray-200 rounded w-1/4 mb-2"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      </div>
                    ))}
                  </div>
                ) : activities.length > 0 ? (
                  activities.map((item) => (
                    <div key={item.id} className="flex flex-col py-1 border-b border-gray-100 last:border-0">
                      <div className="text-primary text-sm font-medium">{item.date}</div>
                      <div className="font-medium">{item.title}</div>
                    </div>
                  ))
                ) : (
                  <div className="text-center text-gray-500">{t('chamber.noUpcomingEvents')}</div>
                )}
              </div>

              <div className="mt-4 text-center">
                <Link href="/events/upcoming" className="block w-full bg-primary text-white py-2 px-4 rounded-md hover:bg-primary/90">
                  {t('chamber.viewMoreEvents')}
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}