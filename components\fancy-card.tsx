"use client"

import type React from "react"

import { motion } from "framer-motion"
import { Card, CardContent } from "@/components/ui/card"
import Image from "next/image"
import { cn } from "@/lib/utils"

interface FancyCardProps {
  title: {
    zh: string
    en: string
  }
  description?: {
    zh: string
    en: string
  }
  image?: string
  icon?: React.ReactNode
  className?: string
  onClick?: () => void
}

export default function FancyCard({ title, description, image, icon, className, onClick }: FancyCardProps) {
  return (
    <motion.div whileHover={{ y: -5 }} whileTap={{ scale: 0.98 }}>
      <Card
        className={cn(
          "overflow-hidden cursor-pointer group hover:shadow-lg hover:shadow-primary/5 transition-all duration-300",
          className,
        )}
        onClick={onClick}
      >
        {image && (
          <div className="relative aspect-[2/1] overflow-hidden">
            <Image
              src={image || "/logo.jpeg"}
              alt={title.en}
              fill
              className="object-cover transition-transform duration-500 group-hover:scale-110"
            />
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          </div>
        )}
        <CardContent
          className={cn("p-6", image ? "relative z-10 -mt-8 bg-gradient-to-t from-background to-background/95" : "")}
        >
          {icon && (
            <div className="w-12 h-12 rounded-lg bg-primary/10 text-primary flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300">
              {icon}
            </div>
          )}
          <h3 className="text-xl font-bold group-hover:text-primary transition-colors duration-300">
            {title.zh}
            <span className="block text-base font-normal text-muted-foreground mt-1">{title.en}</span>
          </h3>
          {description && (
            <p className="mt-2 text-muted-foreground">
              {description.zh}
              <span className="block text-sm mt-1 text-muted-foreground/80">{description.en}</span>
            </p>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}

