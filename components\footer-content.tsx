"use client";

import Link from "next/link";
import Image from "next/image";
import { Mail, Phone, MapPin } from "lucide-react";
import LanguageLink from "@/components/language-link";
import { useSearchParams } from "next/navigation";
import { useTranslation, getLanguageFromParams } from "@/lib/translations";
import { useState, useEffect, useMemo } from "react";
import {
  fetchFooterLinks,
  FooterLink,
  fetchContactInfo,
  ContactInfo,
} from "@/lib/strapi";

export default function FooterContent() {
  const searchParams = useSearchParams();
  const currentLang = getLanguageFromParams(searchParams);
  const { t } = useTranslation(currentLang);

  const [fetchedLinks, setFetchedLinks] = useState<FooterLink[]>([]);
  const [contactInfo, setContactInfo] = useState<ContactInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    let isMounted = true;

    const getStrapiLocale = (lang: string) => {
      switch (lang) {
        case "zh":
          return "zh-Hant-HK";
        case "cn":
          return "zh-Hans-HK";
        case "en":
          return "en";
        default:
          return "en";
      }
    };

    const loadData = async () => {
      try {
        setLoading(true);
        const [footerLinksData, contactInfoData] = await Promise.all([
          fetchFooterLinks({ populate: "localizations" }),
          fetchContactInfo({ locale: getStrapiLocale(currentLang) }),
        ]);

        if (isMounted) {
          setFetchedLinks(footerLinksData);
          setContactInfo(
            contactInfoData.length > 0 ? contactInfoData[0] : null
          );
        }
      } catch (err) {
        if (isMounted) {
          console.error("Error fetching footer data:", err);
          setError(
            err instanceof Error ? err.message : "An unknown error occurred"
          );
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    loadData();

    return () => {
      isMounted = false;
    };
  }, [currentLang]);

  if (loading) {
    return (
      <footer className="bg-[#1E1B4B] text-white">
        <div className="container mx-auto">
          <div className="py-16">
            <div className="animate-pulse">
              <div className="flex flex-col md:flex-row justify-between items-start py-12 border-b border-blue-800">
                <div className="mb-8 md:mb-0">
                  <div className="w-32 h-32 bg-blue-700 rounded mb-6"></div>
                </div>
                <div className="space-y-4">
                  <div className="h-4 bg-blue-700 rounded w-64"></div>
                  <div className="h-4 bg-blue-700 rounded w-56"></div>
                  <div className="h-4 bg-blue-700 rounded w-72"></div>
                </div>
              </div>
              <div className="py-8 border-b border-blue-800">
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {Array(7)
                    .fill(null)
                    .map((_, i) => (
                      <div key={i} className="h-12 bg-blue-700 rounded"></div>
                    ))}
                </div>
              </div>
              <div className="py-6 flex flex-col md:flex-row justify-between items-center">
                <div className="h-4 bg-blue-700 rounded w-48"></div>
                <div className="flex gap-4">
                  <div className="h-4 bg-blue-700 rounded w-16"></div>
                  <div className="h-4 bg-blue-700 rounded w-16"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </footer>
    );
  }

  if (error) {
    return (
      <footer className="bg-[#1E1B4B] text-white py-12">
        <div className="container mx-auto text-center text-red-400">
          <p>Error loading footer data: {error}</p>
        </div>
      </footer>
    );
  }

  return (
    <footer className="bg-[#1E1B4B] text-white">
      <div className="container mx-auto">
        {/* Logo and Contact Section */}
        <div className="flex flex-col md:flex-row justify-between items-start py-12 border-b border-blue-800">
          <div className="mb-8 md:mb-0">
            <div className="relative w-32 h-32 mb-6">
              <Image
                src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/WhatsApp%20Image%202025-02-25%20at%2014.29.07_509a9aaf.jpg-KDCEr9VlnMpFDB5hTK3jEgOuZmC0A7.jpeg"
                alt="KCC Logo"
                fill
                className="object-contain"
              />
            </div>
          </div>
          <div className="space-y-4 text-blue-200">
            <div className="flex items-center gap-3">
              <Mail className="h-5 w-5 text-blue-400" />
              <p>{contactInfo?.email || t("footer.email")}</p>
            </div>
            <div className="flex items-center gap-3">
              <Phone className="h-5 w-5 text-blue-400" />
              <p>{contactInfo?.phonenumber || t("footer.phone")}</p>
            </div>
            <div className="flex items-center gap-3">
              <MapPin className="h-5 w-5 text-blue-400" />
              <p>{contactInfo?.Address || t("footer.address")}</p>
            </div>
          </div>
        </div>

        {/* Navigation Links */}
        <div className="py-8 border-b border-blue-800">
          <nav className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {fetchedLinks.map((link) => {
              const localizedTitle =
                link.localizations?.find((loc) => {
                  const strapiLocaleMap: { [key: string]: string } = {
                    "zh-Hant-HK": "zh",
                    "zh-Hans-HK": "cn",
                    en: "en",
                  };
                  return strapiLocaleMap[loc.locale] === currentLang;
                })?.title || link.title;

              const navigateTo = link.navigateto || "/";

              return (
                <LanguageLink
                  key={link.id}
                  href={navigateTo}
                  className="text-blue-200 hover:text-white transition-colors"
                >
                  <span className="block">{localizedTitle}</span>
                </LanguageLink>
              );
            })}
          </nav>
        </div>

        {/* Copyright */}
        <div className="py-6 flex flex-col md:flex-row justify-between items-center text-sm text-blue-200">
          <div className="flex flex-col md:flex-row items-center gap-2 md:gap-4">
            <p>{t("footer.copyright")}</p>
           
          </div>
          <div className="flex items-center gap-4">
            <Link
              href="https://www.facebook.com/profile.php?id=61565572133389"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white transition-colors"
            >
              {t("footer.facebook")}
            </Link>
            <Link
              href="https://www.instagram.com/hkkcc_1938/"
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-white transition-colors"
            >
              {t("footer.Instagram")}
            </Link>
          </div>
        </div>
      </div>
    </footer>
  );
}
