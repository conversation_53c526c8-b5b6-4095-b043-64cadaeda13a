"use client"
import { useState, useEffect } from "react"
import { usePathname, useSearchParams } from "next/navigation"
import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu, X, ChevronDown, LogOut } from "lucide-react"
import { useUser } from "@/contexts/user-context"
import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
  NavigationMenuViewport,
} from "@/components/ui/navigation-menu"
import { mainNavigation } from "@/app/routes"
import { useTranslation, getLanguageFromParams } from "@/lib/translations"
import LanguageLink from "@/components/language-link"
import axios from 'axios'; // Import axios
import React from 'react'

// Define the structure for dynamic page titles (for fetched data)
interface DynamicPageTitle {
  title: { zh: string; en: string; cn?: string };
  path: string;
  subtitle?: { zh: string; en: string; cn?: string };
}

// Existing MobileMenuNavItem interface
interface MobileMenuNavItem {
  title: { zh: string; en: string; cn?: string };
  path?: string;
  items?: MobileMenuNavItem[];
  subtitle?: { zh: string; en: string; cn?: string };
}

// Define the Strapi API URL (ensure this ENV variable is accessible client-side)
const STRAPI_API_URL = process.env.NEXT_PUBLIC_STRAPI_URL;

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState<string[]>([])
  const { user, logout } = useUser()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const currentLang = getLanguageFromParams(searchParams)
  const { t } = useTranslation(currentLang)

  const [newsLocalizations, setNewsLocalizations] = useState<any[] | null>(null);
  const [newsId, setNewsId] = useState<number | null>(null);
  const [newsLocale, setNewsLocale] = useState<string | null>(null);

  const isNewsDetail = /^\/news\/[0-9]+$/.test(pathname);

  // State to hold dynamically fetched pages
  const [dynamicPages, setDynamicPages] = useState<DynamicPageTitle[]>([]);

  // Effect to fetch news localizations (existing logic)
  useEffect(() => {
    if (typeof window !== 'undefined' && isNewsDetail) {
      setNewsLocalizations((window as any).__NEWS_LOCALIZATIONS__ || null);
      setNewsId((window as any).__NEWS_ID__ || null);
      setNewsLocale((window as any).__NEWS_LOCALE__ || null);
    } else {
      setNewsLocalizations(null);
      setNewsId(null);
      setNewsLocale(null);
    }
  }, [pathname, isNewsDetail]); // Added isNewsDetail to dependencies

  // Effect to fetch dynamic pages from Strapi
  useEffect(() => {
    const fetchDynamicPages = async () => {
      if (!STRAPI_API_URL) {
        console.error("STRAPI_API_URL is not defined. Cannot fetch dynamic pages.");
        return;
      }
      try {
        const res = await axios.get(`${STRAPI_API_URL}/new-pages`, {
          params: {
            'fields[0]': 'title',
            'fields[1]': 'Slug',
            'pagination[limit]': 100 // Fetch up to 100 dynamic pages
          }
        });
        const fetchedPages = res.data.data.map((item: any) => ({
          // Ensure 'title.cn' is always a string
          title: {
            en: item.title || '',
            zh: item.title || '',
            cn: item.title || ''
          },
          path: `/${item.Slug}` // Dynamic pages are served at /<slug>
        }));
        setDynamicPages(fetchedPages);
      } catch (error) {
        console.error("Error fetching dynamic pages for header:", error);
        setDynamicPages([]);
      }
    };
    fetchDynamicPages();
  }, []); // Empty dependency array means this runs once on mount

  const getLocalizedTitle = (titleObj?: { zh: string; en: string; cn?: string }) => {
    if (!titleObj) {
      return '';
    }
    if (currentLang === 'cn') {
      return titleObj.cn || titleObj.zh
    } else if (currentLang === 'zh') {
      return titleObj.zh
    }
    return titleObj.en
  }

  const toggleExpanded = (identifier: string) => { // Renamed from 'path' to 'identifier' for clarity
    setExpandedItems(prev =>
      prev.includes(identifier)
        ? prev.filter(p => p !== identifier)
        : [...prev, identifier]
    )
  }

  const createLanguageLink = (lang: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('lang', lang)
    return `${pathname}?${params.toString()}`
  }

  const getLanguageDisplayName = (lang: string) => {
    switch (lang) {
      case 'zh':
        return '繁'
      case 'cn':
        return '简'
      case 'en':
        return 'EN'
      default:
        return lang.toUpperCase()
    }
  }

  const langToLocale: Record<string, string> = {
    en: 'en',
    zh: 'zh-Hant-HK',
    cn: 'zh-Hans-HK',
  };

  const getNewsLocalizedId = (lang: string) => {
    if (!newsId) return null;
    const targetLocale = langToLocale[lang];
    if (newsLocale === targetLocale) return newsId;
    if (newsLocalizations) {
      const match = newsLocalizations.find((loc: any) => loc.locale === targetLocale);
      return match ? match.id : null;
    }
    return null;
  };

  // Combine mainNavigation with dynamicPages for desktop and mobile menu
  const allNavigationItems = [...mainNavigation];

  if (dynamicPages.length > 0) {
    allNavigationItems.push({
      title: { en: "More+", zh: "更多+", cn: "更多+" }, // Title for the new dropdown
      items: dynamicPages, // List of dynamic pages as sub-items
    });
  }

  // Mobile menu rendering that matches desktop structure
  // Props updated to handle expanded items
  function MobileMenuDesktopMatch({ navigationItems, getLocalizedTitle, t, closeMenu, toggleExpanded, expandedItems }: {
    navigationItems: MobileMenuNavItem[];
    getLocalizedTitle: (titleObj?: { zh: string; en: string; cn?: string }) => string;
    t: (key: string | any) => string;
    closeMenu: () => void;
    toggleExpanded: (identifier: string) => void;
    expandedItems: string[];
  }) {
    return (
      <ul className="space-y-2">
        {navigationItems.map((item: MobileMenuNavItem) => (
          <li key={item.path || item.title.en}>
            {item.items ? (
              <>
                <div
                  className="flex items-center justify-between font-bold text-lg py-2 cursor-pointer"
                  // Use a unique identifier for expansion, e.g., the English title
                  onClick={() => toggleExpanded(item.title.en)}
                >
                  {getLocalizedTitle(item.title)}
                  <ChevronDown className={`h-5 w-5 transition-transform ${expandedItems.includes(item.title.en) ? 'rotate-180' : ''}`} />
                </div>
                {expandedItems.includes(item.title.en) && (
                  <ul className="space-y-2 pl-2">
                    {(item.title.en === "About KCC"
                      ? item.items.filter(subItem => subItem.title.en !== "Council Member + Past Councils" && subItem.title.en !== "KCC Development + Milestones").map((subItem: MobileMenuNavItem) => {
                          if (subItem.title.en === "Affiliated Association") {
                            return (
                              <React.Fragment key={subItem.path || subItem.title.en + '-affiliated'}>
                                <LanguageLink
                                  key={subItem.path || subItem.title.en}
                                  href={subItem.path || '/'}
                                  className="block py-2 text-base pl-4"
                                  onClick={closeMenu}
                                >
                                  <div className="text-sm font-medium leading-none">{getLocalizedTitle(subItem.title)}</div>
                                  {subItem.subtitle && (
                                    <div className="text-xs text-gray-500 mt-1">
                                      {getLocalizedTitle(subItem.subtitle)}
                                    </div>
                                  )}
                                </LanguageLink>
                                <LanguageLink
                                  key="council-member"
                                  href="/about-kcc/council-member"
                                  className="block py-2 text-base pl-4"
                                  onClick={closeMenu}
                                >
                                  <div className="text-sm font-medium leading-none">{t('navigation.councilMember')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  key="past-councils"
                                  href="/history/councilors"
                                  className="block py-2 text-base pl-4"
                                  onClick={closeMenu}
                                >
                                  <div className="text-sm font-medium leading-none">{t('navigation.pastCouncils')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  key="kcc-development"
                                  href="/about-kcc/development-history"
                                  className="block py-2 text-base pl-4"
                                  onClick={closeMenu}
                                >
                                  <div className="text-sm font-medium leading-none">{t('navigation.kccDevelopment')}</div>
                                </LanguageLink>
                                <LanguageLink
                                  key="milestones"
                                  href="/history/milestones"
                                  className="block py-2 text-base pl-4"
                                  onClick={closeMenu}
                                >
                                  <div className="text-sm font-medium leading-none">{t('navigation.milestones')}</div>
                                </LanguageLink>
                              </React.Fragment>
                            );
                          }
                          // Default rendering for all other sub-items
                          return (
                            <LanguageLink
                              key={subItem.path || subItem.title.en}
                              href={subItem.path || '/'}
                              className="block py-2 text-base pl-4"
                              onClick={closeMenu}
                            >
                              <div className="text-sm font-medium leading-none">{getLocalizedTitle(subItem.title)}</div>
                              {subItem.subtitle && (
                                <div className="text-xs text-gray-500 mt-1">
                                  {getLocalizedTitle(subItem.subtitle)}
                                </div>
                              )}
                            </LanguageLink>
                          );
                        })
                      : (item.title.en === "Events"
                        ? item.items.filter(subItem => subItem.title.en !== "Chamber Activities")
                        : item.items
                      ).map((subItem: MobileMenuNavItem) => {
                        if (subItem.title.en === "Events Review") {
                          return (
                            <div key={subItem.title.en}>
                              <LanguageLink
                                href={subItem.path || '/'}
                                className="block py-2 text-base pl-4 font-semibold"
                                onClick={closeMenu}
                              >
                                {getLocalizedTitle(subItem.title)}
                              </LanguageLink>
                              <div className="pl-8">
                                <LanguageLink href="/events/new-era" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.newEra')}
                                </LanguageLink>
                                <LanguageLink href="/events/elderly-activities" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.elderlyActivities')}
                                </LanguageLink>
                                <LanguageLink href="/events/youth-festival" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.youthFestival')}
                                </LanguageLink>
                                <LanguageLink href="/events/forums" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.forums')}
                                </LanguageLink>
                              </div>
                            </div>
                          );
                        }
                        if (subItem.title.en === "Social Welfare Activities") {
                          return (
                            <div key={subItem.title.en}>
                              <LanguageLink
                                href="/events/social-charity"
                                className="block py-2 text-base pl-4 font-semibold"
                                onClick={closeMenu}
                              >
                                Social Charity
                              </LanguageLink>
                              <div className="pl-8">
                                <LanguageLink href="/events/care-team" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.careTeam')}
                                </LanguageLink>
                                <LanguageLink href="/about-kcc/elderly-center" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.elderlyCenter')}
                                </LanguageLink>
                                <LanguageLink href="/events/sea-scouts" className="block py-2 text-base" onClick={closeMenu}>
                                  {t('activities.seaScouts')}
                                </LanguageLink>
                              </div>
                            </div>
                          );
                        }
                        // Default rendering for all other sub-items
                        return (
                          <LanguageLink
                            key={subItem.path || subItem.title.en}
                            href={subItem.path || '/'}
                            className="block py-2 text-base pl-4"
                            onClick={closeMenu}
                          >
                            <div className="text-sm font-medium leading-none">{getLocalizedTitle(subItem.title)}</div>
                            {subItem.subtitle && (
                              <div className="text-xs text-gray-500 mt-1">
                                {getLocalizedTitle(subItem.subtitle)}
                              </div>
                            )}
                          </LanguageLink>
                        );
                      })
                    )}
                  </ul>
                )}
              </>
            ) : (
              <LanguageLink
                href={item.path || '/'}
                className="block py-2 text-lg font-bold"
                onClick={closeMenu}
              >
                {getLocalizedTitle(item.title)}
              </LanguageLink>
            )}
          </li>
        ))}
      </ul>
    );
  }

  return (
    <header className="w-full">
      {/* Top Bar */}
      <div className="bg-[#1E1B4B] text-white">
        <div className="container">
          <div className="flex justify-end h-8 items-center text-sm">
            {user ? (
              <div className="flex items-center gap-4">
                <LanguageLink href="/dashboard" className="hover:underline">
                  {t('header.welcome')}, {user.username}
                </LanguageLink>
                <button
                  onClick={logout}
                  className="flex items-center gap-1 hover:underline"
                >
                  <LogOut className="h-4 w-4" />
                  {t('header.logout')}
                </button>
              </div>
            ) : (
              <>
                <LanguageLink href="/login" className="hover:underline">
                  {t('header.login')}
                </LanguageLink>
                <span className="mx-2">|</span>
                <LanguageLink href="/register" className="hover:underline">
                  {t('header.register')}
                </LanguageLink>
                <span className="mx-2">|</span>
              </>
            )}
            <div className="flex items-center gap-2">
              {['zh', 'cn', 'en'].map((code) => {
                // If on news detail page, use news logic
                if (isNewsDetail) {
                  const targetId = getNewsLocalizedId(code);
                  const isActive = currentLang === code;
                  return targetId ? (
                    <Link
                      key={code}
                      href={`/news/${targetId}?lang=${code}`}
                      className={`hover:underline ${isActive ? 'font-bold' : ''}`}
                    >
                      {getLanguageDisplayName(code)}
                    </Link>
                  ) : (
                    <span key={code} className="text-gray-400 cursor-not-allowed">{getLanguageDisplayName(code)}</span>
                  );
                } else {
                  // Default site-wide language switcher
                  return (
                    <Link
                      key={code}
                      href={createLanguageLink(code)}
                      className={`hover:underline ${currentLang === code ? 'font-bold' : ''}`}
                    >
                      {getLanguageDisplayName(code)}
                    </Link>
                  );
                }
              })}
            </div>
          </div>
        </div>
      </div>

      {/* Main Header */}
      <div className="bg-white border-b">
        <div className="container">
          <div className="flex h-20 items-center justify-between">
            <LanguageLink href="/" className="flex items-center gap-3">
              <div className="relative w-16 h-16">
                <Image
                  src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/WhatsApp%20Image%202025-02-25%20at%2014.29.07_509a9aaf.jpg-KDCEr9VlnMpFDB5hTK3jEgOuZmC0A7.jpeg"
                  alt="KCC Logo"
                  fill
                  className="object-contain"
                />
              </div>
              <div className="flex flex-col">
                <span className="text-lg font-bold text-[#1E1B4B]">{t('header.companyNameShort')}</span>
                {currentLang === 'en' && (
                  <span className="text-sm text-gray-600">{t('header.companyName')}</span>
                )}
              </div>
            </LanguageLink>

            {/* Desktop Navigation */}
            <nav className="hidden lg:block">
              <NavigationMenu>
                <NavigationMenuList>
                  {/* Use allNavigationItems here */}
                  {allNavigationItems.map((item: MobileMenuNavItem) => (
                    <NavigationMenuItem key={item.path || item.title.en}>
                      {item.items ? (
                        <>
                          <NavigationMenuTrigger>
                            <span className="flex items-center gap-1">
                              {getLocalizedTitle(item.title)}
                            </span>
                          </NavigationMenuTrigger>
                          <NavigationMenuContent>
                            {/* Adjusted width for better display of dynamic items if many */}
                            <ul className="grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-1">
                              {item.items.map((subItem: MobileMenuNavItem) => (
                                <li key={subItem.path || subItem.title.en}>
                                  <NavigationMenuLink asChild>
                                    {(() => {
                                      // Existing custom rendering logic
                                      if (subItem.title.en === "Upcoming Events") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href={subItem.path || '/'}
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {getLocalizedTitle(subItem.title)}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Chamber Activities") {
                                        return (
                                          <div className="grid grid-cols-2 select-none rounded-md p-3 leading-none no-underline outline-none gap-2">
                                            <LanguageLink
                                              href="/events/new-era"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.newEra')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/elderly-activities"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.elderlyActivities')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/youth-festival"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.youthFestival')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/events/forums"
                                              className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('activities.forums')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (
                                        subItem.title.en === "Suggestion Box" &&
                                        item.title.en === "Contact Us" &&
                                        item.items?.some(i => i.title.en === "Contact Us")
                                      ) {
                                        const contactUsItem = item.items.find(i => i.title.en === "Contact Us");
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none gap-2">
                                            <LanguageLink
                                              href="/suggestion"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {getLocalizedTitle(subItem.title)}
                                              </div>
                                            </LanguageLink>
                                            {contactUsItem && (
                                              <LanguageLink
                                                href={contactUsItem.path || '/'}
                                                className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                              >
                                                <div className="text-sm font-medium leading-none">
                                                  {getLocalizedTitle(contactUsItem.title)}
                                                </div>
                                              </LanguageLink>
                                            )}
                                          </div>
                                        );
                                      } else if (
                                        subItem.title.en === "Contact Us" &&
                                        item.title.en === "Contact Us" &&
                                        item.items?.some(i => i.title.en === "Suggestion Box")
                                      ) {
                                        return null;
                                      } else if (subItem.title.en === "Council Member + Past Councils") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/about-kcc/council-member"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.councilMember')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/history/councilors"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.pastCouncils')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "KCC Development + Milestones") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/about-kcc/development-history"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.kccDevelopment')}
                                              </div>
                                            </LanguageLink>
                                            <LanguageLink
                                              href="/history/milestones"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.milestones')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Reception + Monthly Meetings") {
                                        return (
                                          <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                            <LanguageLink
                                              href="/events/reception"
                                              className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                            >
                                              <div className="text-sm font-medium leading-none">
                                                {t('navigation.receptionGuestsMonthlyMeeting')}
                                              </div>
                                            </LanguageLink>
                                          </div>
                                        );
                                      } else if (subItem.title.en === "Social Welfare Activities") {
                                        return (
                                          <div>
                                            <div className="flex select-none rounded-md p-3 leading-none no-underline outline-none">
                                              <LanguageLink
                                                href="/events/social-charity"
                                                className="flex-1 hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded"
                                              >
                                                <div className="text-sm font-medium leading-none">
                                                  {t('activities.socialCharity')}
                                                </div>
                                              </LanguageLink>
                                            </div>
                                            <div className="grid grid-cols-3 select-none rounded-md p-3 leading-none no-underline outline-none gap-2">
                                              <LanguageLink
                                                href="/events/care-team"
                                                className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                              >
                                                <div className="text-sm font-medium leading-none">
                                                  {t('activities.careTeam')}
                                                </div>
                                              </LanguageLink>
                                              <LanguageLink
                                                href="/about-kcc/elderly-center"
                                                className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                              >
                                                <div className="text-sm font-medium leading-none">
                                                  {t('activities.elderlyCenter')}
                                                </div>
                                              </LanguageLink>
                                              <LanguageLink
                                                href="/events/sea-scouts"
                                                className="hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground p-2 rounded text-center"
                                              >
                                                <div className="text-sm font-medium leading-none">
                                                  {t('activities.seaScouts')}
                                                </div>
                                              </LanguageLink>
                                            </div>
                                          </div>
                                        );
                                      } else {
                                        // Default rendering for ALL other sub-items, including dynamic pages
                                        return (
                                          <LanguageLink
                                            href={subItem.path || '/'}
                                            className="block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground"
                                          >
                                            <div className="text-sm font-medium leading-none">{getLocalizedTitle(subItem.title)}</div>
                                            {subItem.subtitle && (
                                              <div className="text-xs text-gray-500 mt-1">
                                                {getLocalizedTitle(subItem.subtitle)}
                                              </div>
                                            )}
                                          </LanguageLink>
                                        );
                                      }
                                    })()}
                                  </NavigationMenuLink>
                                </li>
                              ))}
                            </ul>
                          </NavigationMenuContent>
                        </>
                      ) : (
                        <Link href={item.path || '/'} legacyBehavior passHref>
                          <NavigationMenuLink className="group inline-flex h-10 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium transition-colors hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus:outline-none disabled:pointer-events-none disabled:opacity-50 data-[active]:bg-accent/50 data-[state=open]:bg-accent/50">
                            {getLocalizedTitle(item.title)}
                          </NavigationMenuLink>
                        </Link>
                      )}
                    </NavigationMenuItem>
                  ))}
                </NavigationMenuList>
                <NavigationMenuViewport />
              </NavigationMenu>
            </nav>

            {/* Mobile Menu Button */}
            <button
              className="lg:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              {isMenuOpen ? (
                <X className="h-6 w-6" />
              ) : (
                <Menu className="h-6 w-6" />
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Panel */}
      {isMenuOpen && (
        <div className="fixed inset-0 z-50 bg-white lg:hidden overflow-y-auto">
          <div className="container py-6">
            <div className="flex justify-between items-center mb-6">
              <LanguageLink href="/" className="flex items-center gap-3" onClick={() => setIsMenuOpen(false)}>
                <div className="relative w-12 h-12">
                  <Image
                    src="https://hebbkx1anhila5yf.public.blob.vercel-storage.com/WhatsApp%20Image%202025-02-25%20at%2014.29.07_509a9aaf.jpg-KDCEr9VlnMpFDB5hTK3jEgOuZmC0A7.jpeg"
                    alt="KCC Logo"
                    fill
                    className="object-contain"
                  />
                </div>
                <div className="flex flex-col">
                  <span className="text-lg font-bold text-[#1E1B4B]">{t('header.companyNameShort')}</span>
                  {currentLang === 'en' && (
                    <span className="text-sm text-gray-600">{t('header.companyName')}</span>
                  )}
                </div>
              </LanguageLink>
              <button
                onClick={() => setIsMenuOpen(false)}
                aria-label="Close menu"
                className="p-2"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Pass allNavigationItems and expansion states/handlers to mobile menu */}
            <MobileMenuDesktopMatch
              navigationItems={allNavigationItems}
              getLocalizedTitle={getLocalizedTitle}
              t={t}
              closeMenu={() => setIsMenuOpen(false)}
              toggleExpanded={toggleExpanded}
              expandedItems={expandedItems}
            />

            {/* Mobile Login/Register/Logout */}
            <div className="mt-8 pt-4 border-t border-gray-200 flex flex-col gap-4">
              {user ? (
                <>
                  <LanguageLink href="/dashboard" className="block py-2 text-base font-bold" onClick={() => setIsMenuOpen(false)}>
                    {t('header.welcome')}, {user.username}
                  </LanguageLink>
                  <Button
                    onClick={() => {
                      logout();
                      setIsMenuOpen(false);
                    }}
                    className="w-full justify-start text-base font-bold"
                    variant="ghost"
                  >
                    <LogOut className="h-5 w-5 mr-2" />
                    {t('header.logout')}
                  </Button>
                </>
              ) : (
                <>
                  <LanguageLink href="/login" className="block py-2 text-base font-bold" onClick={() => setIsMenuOpen(false)}>
                    {t('header.login')}
                  </LanguageLink>
                  <LanguageLink href="/register" className="block py-2 text-base font-bold" onClick={() => setIsMenuOpen(false)}>
                    {t('header.register')}
                  </LanguageLink>
                </>
              )}
              <div className="flex gap-4 mt-4">
                {['zh', 'cn', 'en'].map((code) => {
                  if (isNewsDetail) {
                    const targetId = getNewsLocalizedId(code);
                    const isActive = currentLang === code;
                    return targetId ? (
                      <Link
                        key={code}
                        href={`/news/${targetId}?lang=${code}`}
                        className={`text-lg font-bold ${isActive ? 'underline' : ''}`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {getLanguageDisplayName(code)}
                      </Link>
                    ) : (
                      <span key={code} className="text-gray-400 text-lg font-bold cursor-not-allowed">{getLanguageDisplayName(code)}</span>
                    );
                  } else {
                    return (
                      <Link
                        key={code}
                        href={createLanguageLink(code)}
                        className={`text-lg font-bold ${currentLang === code ? 'underline' : ''}`}
                        onClick={() => setIsMenuOpen(false)}
                      >
                        {getLanguageDisplayName(code)}
                      </Link>
                    );
                  }
                })}
              </div>
            </div>
          </div>
        </div>
      )}
    </header>
  )
}