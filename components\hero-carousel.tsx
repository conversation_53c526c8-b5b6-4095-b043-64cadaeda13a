"use client"

import { useState, useEffect } from "react"
import Image from "next/image"
import { Button } from "@/components/ui/button"
import { ChevronLeft, ChevronRight } from "lucide-react"

const slides = [
  {
    image: "/logo.jpeg",
    alt: "Slide 1",
    link: "/event/2fe8c5c7f15d/976ddbd9bb4a",
  },
  {
    image: "/logo.jpeg",
    alt: "Slide 2",
    link: "/event/6a069f756a13/be0668762516",
  },
  // Add more slides as needed
]

export default function HeroCarousel() {
  const [current, setCurrent] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrent((current) => (current === slides.length - 1 ? 0 : current + 1))
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  const prev = () => {
    setCurrent((current) => (current === 0 ? slides.length - 1 : current - 1))
  }

  const next = () => {
    setCurrent((current) => (current === slides.length - 1 ? 0 : current + 1))
  }

  return (
    <div className="relative w-full h-[400px] md:h-[600px] overflow-hidden">
      {slides.map((slide, index) => (
        <div
          key={index}
          className={`absolute inset-0 transition-opacity duration-1000 ${
            index === current ? "opacity-100" : "opacity-0"
          }`}
        >
          <Image
            src={slide.image || "/logo.jpeg"}
            alt={slide.alt}
            fill
            className="object-cover"
            priority={index === 0}
          />
        </div>
      ))}

      <div className="absolute inset-0 flex items-center justify-between p-4">
        <Button
          variant="ghost"
          size="icon"
          className="h-12 w-12 rounded-full bg-background/50 hover:bg-background/80"
          onClick={prev}
        >
          <ChevronLeft className="h-6 w-6" />
        </Button>
        <Button
          variant="ghost"
          size="icon"
          className="h-12 w-12 rounded-full bg-background/50 hover:bg-background/80"
          onClick={next}
        >
          <ChevronRight className="h-6 w-6" />
        </Button>
      </div>

      <div className="absolute bottom-4 left-0 right-0 flex justify-center gap-2">
        {slides.map((_, index) => (
          <button
            key={index}
            className={`h-2 w-2 rounded-full transition-all ${index === current ? "bg-primary w-4" : "bg-primary/50"}`}
            onClick={() => setCurrent(index)}
          />
        ))}
      </div>
    </div>
  )
}

