'use client'

import { useEffect, useState } from 'react'

interface HydrationErrorBoundaryProps {
  children: React.ReactNode
  fallback?: React.ReactNode
}

export default function HydrationErrorBoundary({ 
  children, 
  fallback = <div>Loading...</div> 
}: HydrationErrorBoundaryProps) {
  const [isHydrated, setIsHydrated] = useState(false)
  const [hasError, setHasError] = useState(false)

  useEffect(() => {
    // Check if we're in the browser
    if (typeof window !== 'undefined') {
      setIsHydrated(true)
      
      // Listen for hydration errors
      const handleError = (event: ErrorEvent) => {
        if (event.message.includes('Hydration failed') || 
            event.message.includes('Text content does not match server-rendered HTML')) {
          console.warn('Hydration mismatch detected, likely caused by browser extension:', event.message)
          setHasError(true)
        }
      }

      window.addEventListener('error', handleError)
      
      return () => {
        window.removeEventListener('error', handleError)
      }
    }
  }, [])

  // Show fallback during hydration or if there's an error
  if (!isHydrated || hasError) {
    return <>{fallback}</>
  }

  return <>{children}</>
} 