"use client"

import { useState, useEffect } from "react"
import Image from "next/image"

interface ImageSliderProps {
  images: Array<{
    id: number
    url?: string
    name?: string
    documentId?: string
    formats?: {
      large?: { url: string }
      medium?: { url: string }
      small?: { url: string }
      thumbnail?: { url: string }
    }
  }>
  interval?: number // Time in milliseconds between slides
  alt: string
}

export default function ImageSlider({ images, interval = 2000, alt }: ImageSliderProps) {
  const [currentIndex, setCurrentIndex] = useState(0)

  useEffect(() => {
    // Only set up the interval if there are multiple images
    if (images.length <= 1) return

    const timer = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % images.length)
    }, interval)

    return () => clearInterval(timer)
  }, [images.length, interval])

  // If no images, show a placeholder
  if (images.length === 0) {
    return (
      <div className="relative aspect-video bg-gray-200">
        <Image
          src="/logo.jpeg"
          alt={alt}
          fill
          className="object-cover"
        />
      </div>
    )
  }

  return (
    <div className="relative aspect-video overflow-hidden">
      {images.map((image, index) => {
        // Get the image URL from various possible sources
        let imageUrl = "";

        // First try to get URL directly from the image object
        if (image.url) {
          imageUrl = image.url;
        }

        // If no direct URL, try to get from formats
        else if (image.formats) {
          if (image.formats.medium?.url) {
            imageUrl = image.formats.medium.url;
          } else if (image.formats.small?.url) {
            imageUrl = image.formats.small.url;
          } else if (image.formats.large?.url) {
            imageUrl = image.formats.large.url;
          } else if (image.formats.thumbnail?.url) {
            imageUrl = image.formats.thumbnail.url;
          }
        }

        // If still no URL, use placeholder
        if (!imageUrl) {
          imageUrl = "/logo.jpeg";
          console.warn(`No URL found for image at index ${index}`);
        }

       

        return (
          <div
            key={image.id || index}
            className={`absolute inset-0 transition-opacity duration-1000 ${
              index === currentIndex ? "opacity-100" : "opacity-0"
            }`}
          >
            <Image
              src={imageUrl}
              alt={`${alt} - image ${index + 1}`}
              fill
              className="object-cover"
              priority={index === 0} // Load the first image with priority
              onError={(e) => {
                console.error(`Error loading image: ${imageUrl}`);
                // Fallback to placeholder if image fails to load
                (e.target as HTMLImageElement).src = "/logo.jpeg";
              }}
            />
          </div>
        )
      })}

      {/* Dots indicator for multiple images */}
      {images.length > 1 && (
        <div className="absolute bottom-3 left-0 right-0 flex justify-center gap-1.5">
          {images.map((_, index) => (
            <button
              key={index}
              className={`w-2 h-2 rounded-full ${
                index === currentIndex ? "bg-white" : "bg-white/50"
              }`}
              onClick={() => setCurrentIndex(index)}
              aria-label={`Go to slide ${index + 1}`}
            />
          ))}
        </div>
      )}
    </div>
  )
}