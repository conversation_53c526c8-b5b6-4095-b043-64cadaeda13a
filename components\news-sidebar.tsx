import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import Image from "next/image"
import Link from "next/link"

const news = [
  {
    id: 1,
    title: "九龍總商會新紀元精英培訓計劃 2024",
    date: "2024-02-21",
    image: "/logo.jpeg",
    category: "活動",
  },
  {
    id: 2,
    title: "與政府代表會面交流會",
    date: "2024-02-18",
    image: "/logo.jpeg",
    category: "新聞",
  },
  {
    id: 3,
    title: "2024年度會員大會",
    date: "2024-02-15",
    image: "/logo.jpeg",
    category: "通告",
  },
  {
    id: 4,
    title: "商會代表團訪問大灣區",
    date: "2024-02-10",
    image: "/logo.jpeg",
    category: "活動",
  },
]

export default function NewsSidebar() {
  return (
    <aside className="space-y-6">
      <h2 className="text-2xl font-bold text-[#002B5C]">最新消息</h2>

      {news.map((item) => (
        <Link href={`/news/${item.id}`} key={item.id}>
          <Card className="hover:shadow-md transition-all duration-300">
            <CardContent className="p-4">
              <div className="relative aspect-video mb-4">
                <Image src={item.image || "/logo.jpeg"} alt={item.title} fill className="object-cover rounded" />
              </div>
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary" className="bg-[#002B5C]/10 text-[#002B5C] hover:bg-[#002B5C]/20">
                  {item.category}
                </Badge>
                <time className="text-sm text-gray-500">{item.date}</time>
              </div>
              <h3 className="font-medium line-clamp-2 hover:text-[#FF6B00] transition-colors">{item.title}</h3>
            </CardContent>
          </Card>
        </Link>
      ))}

      <Link href="/news" className="inline-flex items-center text-[#FF6B00] hover:text-[#FF6B00]/80 text-sm">
        查看更多新聞 →
      </Link>
    </aside>
  )
}

