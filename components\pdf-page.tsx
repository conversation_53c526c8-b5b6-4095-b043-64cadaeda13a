"use client"

import { useEffect, useRef, memo } from "react"
// type RenderTask is defined below for local use
type RenderTask = any;

// Define types for PDF.js objects for better type safety
interface PDFJSPage {
  getViewport: (options: { scale: number; rotation: number }) => any
  render: (params: any) => RenderTask
  getTextContent: () => Promise<any>
  rotate: number
}

interface PDFJSDocument {
  numPages: number
  getPage: (pageNumber: number) => Promise<PDFJSPage>
}

interface PdfPageProps {
  pdfDoc: PDFJSDocument
  pageNum: number
  scale: number
  rotation: number
}

const PdfPage = memo(function PdfPage({ pdfDoc, pageNum, scale, rotation }: PdfPageProps) {
  const canvasRef = useRef<HTMLCanvasElement | null>(null)
  const textLayerRef = useRef<HTMLDivElement | null>(null)
  const containerRef = useRef<HTMLDivElement | null>(null)
  const renderTaskRef = useRef<RenderTask | null>(null)

  useEffect(() => {
    const render = async () => {
      // Cancel any in-progress render before starting a new one
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel()
      }
      try {
        // --- Responsive scale for small screens ---
        let effectiveScale = scale
        let cssWidth = undefined
        let cssHeight = undefined
        if (typeof window !== 'undefined') {
          const isSmallScreen = window.innerWidth < 640
          if (isSmallScreen) {
            effectiveScale = scale * window.devicePixelRatio
          }
        }
        const page = await pdfDoc.getPage(pageNum)
        const pageRotation = page.rotate;
        const viewport = page.getViewport({ scale: effectiveScale, rotation: pageRotation + rotation })

        const canvas = canvasRef.current
        const textLayer = textLayerRef.current
        const container = containerRef.current
        if (!canvas || !textLayer || !container) return

        const context = canvas.getContext("2d")
        if (!context) return

        // Set canvas pixel size
        canvas.height = viewport.height
        canvas.width = viewport.width
        // Set CSS size for canvas (fit to screen, not pixel size)
        if (typeof window !== 'undefined') {
          const isSmallScreen = window.innerWidth < 640
          if (isSmallScreen) {
            // Fit to screen width
            cssWidth = `${Math.floor(viewport.width / window.devicePixelRatio)}px`
            cssHeight = `${Math.floor(viewport.height / window.devicePixelRatio)}px`
          } else {
            cssWidth = `${viewport.width}px`
            cssHeight = `${viewport.height}px`
          }
          canvas.style.width = cssWidth
          canvas.style.height = cssHeight
          container.style.width = cssWidth
          container.style.height = cssHeight
        } else {
          canvas.style.width = `${viewport.width}px`
          canvas.style.height = `${viewport.height}px`
          container.style.width = `${viewport.width}px`
          container.style.height = `${viewport.height}px`
        }

        renderTaskRef.current = page.render({
          canvasContext: context,
          viewport: viewport,
        })
        await renderTaskRef.current.promise

        // --- Hide garbled text: do NOT render text layer ---
        // const textContent = await page.getTextContent()
        // textLayer.innerHTML = "" // Clear previous text
        // textLayer.style.width = `${viewport.width}px`
        // textLayer.style.height = `${viewport.height}px`
        // textLayer.style.setProperty("--scale-factor", viewport.scale.toString())
        // window.pdfjsLib.renderTextLayer({
        //   textContentSource: textContent,
        //   container: textLayer,
        //   viewport: viewport,
        //   textDivs: [],
        // })
      } catch (e: any) {
        // Suppress RenderingCancelledException and canvas reuse error
        if (
          (e?.name && e.name === "RenderingCancelledException") ||
          (typeof e?.message === 'string' && e.message.includes('Cannot use the same canvas during multiple render() operations'))
        ) {
          // Ignore these errors
        }
      }
    }

    render()

    return () => {
      if (renderTaskRef.current) {
        renderTaskRef.current.cancel()
      }
    }
  }, [pdfDoc, pageNum, scale, rotation])

  return (
    <div ref={containerRef} className="relative bg-white shadow-lg">
      <canvas ref={canvasRef} />
      <div ref={textLayerRef} className="textLayer" />
    </div>
  )
})

export default PdfPage
