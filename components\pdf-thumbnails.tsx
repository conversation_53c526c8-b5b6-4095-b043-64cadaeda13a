"use client"

import { useEffect, useRef, memo } from "react"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"
import type { PDFDocumentProxy } from "pdfjs-dist"

interface PdfThumbnailsProps {
  pdfDoc: PDFDocumentProxy
  currentPage: number
  onThumbnailClick: (page: number) => void
}

const PdfThumbnails = memo(function PdfThumbnails({ pdfDoc, currentPage, onThumbnailClick }: PdfThumbnailsProps) {
  const thumbnailRefs = useRef<(HTMLCanvasElement | null)[]>([])

  useEffect(() => {
    const renderThumbnails = async () => {
      for (let i = 1; i <= pdfDoc.numPages; i++) {
        const canvas = thumbnailRefs.current[i - 1]
        if (canvas) {
          const page = await pdfDoc.getPage(i)
          const viewport = page.getViewport({ scale: 0.2 }) // Small scale for thumbnails
          const context = canvas.getContext("2d")
          if (context) {
            canvas.height = viewport.height
            canvas.width = viewport.width
            page.render({ canvasContext: context, viewport: viewport })
          }
        }
      }
    }
    renderThumbnails()
  }, [pdfDoc])

  return (
    <ScrollArea className="h-full bg-gray-100 dark:bg-gray-800 p-2">
      <div className="space-y-2">
        {Array.from({ length: pdfDoc.numPages }, (_, i) => (
          <div
            key={`thumb-${i + 1}`}
            className={cn(
              "p-1 border-2 rounded-md cursor-pointer transition-all",
              currentPage === i + 1 ? "border-primary" : "border-transparent hover:border-gray-400",
            )}
            onClick={() => onThumbnailClick(i + 1)}
          >
            <canvas ref={(el) => (thumbnailRefs.current[i] = el)} className="w-full h-auto shadow-md" />
            <p className="text-center text-xs mt-1">{i + 1}</p>
          </div>
        ))}
      </div>
    </ScrollArea>
  )
})

export default PdfThumbnails
