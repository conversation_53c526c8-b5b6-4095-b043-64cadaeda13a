"use client"

import type React from "react"

import { useState, type ChangeEvent, useEffect, useRef, useCallback } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { ScrollArea } from "@/components/ui/scroll-area"
import {
  XCircle,
  FileText,
  Loader2,
  ChevronLeft,
  ChevronRight,
  ZoomIn,
  ZoomOut,
  RotateCw,
  Expand,
  Download,
  PanelLeft,
} from "lucide-react"
import PdfPage from "./pdf-page"
import PdfThumbnails from "./pdf-thumbnails"
import type { PDFDocumentProxy } from "pdfjs-dist"

// Declare pdfjsLib globally
declare global {
  interface Window {
    pdfjsLib: any
  }
}

const PDFJS_CDN_URL = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"
const PDFJS_WORKER_CDN_URL = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js"

interface PdfViewerProps {
  pdfUrl: string;
}

export default function PdfViewer({ pdfUrl }: PdfViewerProps) {
  const [error, setError] = useState<string | null>(null)
  const [isLoading, setIsLoading] = useState<boolean>(false)

  const [pdfDoc, setPdfDoc] = useState<PDFDocumentProxy | null>(null)
  const [currentPageNum, setCurrentPageNum] = useState<number>(1)
  const [totalPages, setTotalPages] = useState<number>(0)
  const DEFAULT_DESKTOP_SCALE = 1.4;
  const [scale, setScale] = useState<number>(typeof window !== 'undefined' && window.innerWidth >= 768 ? DEFAULT_DESKTOP_SCALE : 1.5)
  const [rotation, setRotation] = useState<number>(0)
  const [pageInput, setPageInput] = useState<string>("1")
  const [thumbnailsVisible, setThumbnailsVisible] = useState<boolean>(true)

  const viewerRef = useRef<HTMLDivElement | null>(null)
  const pdfJsLoaded = useRef(false)

  const loadPdfJs = useCallback(() => {
    if (pdfJsLoaded.current || window.pdfjsLib) {
      if (window.pdfjsLib) window.pdfjsLib.GlobalWorkerOptions.workerSrc = PDFJS_WORKER_CDN_URL
      pdfJsLoaded.current = true
      return Promise.resolve()
    }
    return new Promise<void>((resolve, reject) => {
      // Check if script is already loading
      const existingScript = document.querySelector(`script[src="${PDFJS_CDN_URL}"]`)
      if (existingScript) {
        existingScript.addEventListener('load', () => {
          window.pdfjsLib.GlobalWorkerOptions.workerSrc = PDFJS_WORKER_CDN_URL
          pdfJsLoaded.current = true
          resolve()
        })
        return
      }

      const script = document.createElement("script")
      script.src = PDFJS_CDN_URL
      script.onload = () => {
        window.pdfjsLib.GlobalWorkerOptions.workerSrc = PDFJS_WORKER_CDN_URL
        pdfJsLoaded.current = true
        resolve()
      }
      script.onerror = () => reject(new Error("Failed to load PDF.js library."))
      document.head.appendChild(script) // Use head instead of body
    })
  }, [])

  useEffect(() => {
    loadPdfJs().catch((err) => setError(err.message))
  }, [loadPdfJs])

  useEffect(() => {
    return () => {
      pdfDoc?.destroy().catch(() => {})
    }
  }, [pdfDoc])

  useEffect(() => {
    const loadPdf = async () => {
      setIsLoading(true)
      setError(null)
      setPdfDoc(null)
      try {
        await loadPdfJs()
        const loadedPdfDoc = await window.pdfjsLib.getDocument(pdfUrl).promise
        setPdfDoc(loadedPdfDoc)
        setTotalPages(loadedPdfDoc.numPages)
        setCurrentPageNum(1)
        setPageInput("1")
        setRotation(0)
        setTimeout(() => fitTo('width'), 100)
      } catch (err) {
        setError(err instanceof Error ? err.message : "Failed to load PDF.")
      } finally {
        setIsLoading(false)
      }
    }
    loadPdf()
    const handleResize = () => fitTo('width')
    window.addEventListener('resize', handleResize)
    return () => {
      window.removeEventListener('resize', handleResize)
    }
  }, [loadPdfJs, pdfUrl])

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPageInput(e.target.value)
  }

  const handlePageInputSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const newPageNum = Number.parseInt(pageInput, 10)
    if (newPageNum >= 1 && newPageNum <= totalPages) {
      setCurrentPageNum(newPageNum)
    } else {
      setPageInput(currentPageNum.toString())
    }
  }

  const onPrevPage = () => setCurrentPageNum((prev) => (prev > 1 ? prev - 1 : 1))
  const onNextPage = () => setCurrentPageNum((prev) => (prev < totalPages ? prev + 1 : totalPages))

  useEffect(() => {
    setPageInput(currentPageNum.toString())
  }, [currentPageNum])

  const handleZoom = (newScale: number) => setScale(Math.max(0.5, Math.min(newScale, 3.0)))
  const handleRotate = () => setRotation((prev) => (prev + 90) % 360)

  const fitTo = (type: "page" | "width") => {
    if (!pdfDoc || !viewerRef.current) return
    pdfDoc.getPage(currentPageNum).then((page) => {
      const viewport = page.getViewport({ scale: 1, rotation })
      let viewerWidth = viewerRef.current?.getBoundingClientRect().width || 0
      if (window.innerWidth < 768) {
        viewerWidth = window.innerWidth
      }
      const viewerHeight = viewerRef.current?.getBoundingClientRect().height || 0
      if (window.innerWidth >= 768) {
        setScale(DEFAULT_DESKTOP_SCALE)
      } else {
        const scale =
          type === "page"
            ? Math.min(viewerWidth / viewport.width, viewerHeight / viewport.height) * 0.95
            : (viewerWidth / viewport.width) * 0.95
        setScale(scale)
      }
    })
  }

  useEffect(() => {
    if (window.innerWidth >= 768) {
      setScale(DEFAULT_DESKTOP_SCALE)
    } else {
      fitTo('width')
    }
  }, [currentPageNum, rotation])

  const handleDownload = async () => {
    try {
      const response = await fetch(pdfUrl)
      const blob = await response.blob()
      const url = URL.createObjectURL(blob)
      const a = document.createElement("a")
      a.href = url
      a.download = "KCC Development  KCC 75 年發展史_revised.pdf"
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (err) {
      setError("Failed to download PDF.")
    }
  }

  const handleFullscreen = () => {
    if (!viewerRef.current) return
    if (document.fullscreenElement) {
      document.exitFullscreen()
    } else {
      viewerRef.current.requestFullscreen()
    }
  }



  if (!pdfDoc) {
    return (
      <div className="min-h-screen flex flex-col items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
        <div className="w-full max-w-lg text-center">
          <FileText className="mx-auto h-16 w-16 text-gray-400" />
          <h1 className="mt-4 text-2xl font-bold">PDF Document Viewer</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-300">Loading PDF document...</p>
          {isLoading && <Loader2 className="mx-auto mt-4 h-6 w-6 animate-spin" />}
          {error && (
            <Alert variant="destructive" className="mt-4 text-left">
              <XCircle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </div>
      </div>
    )
  }

  return (
    <TooltipProvider>
      <style>{`
        @media (max-width: 767px) {
          canvas {
            max-width: 100vw !important;
            height: auto !important;
          }
        }
      `}</style>
      <div className="w-full min-h-[60vh] max-w-7xl mx-auto flex flex-col bg-gray-200 dark:bg-gray-800" ref={viewerRef}>
        {/* Toolbar */}
        <div className="flex-shrink-0 bg-white dark:bg-gray-900 shadow-md flex flex-col md:flex-row items-start md:items-center justify-between px-2 md:px-4 z-10 gap-2 md:gap-0 min-h-16 w-full">
          {/* Filename row */}
          <div className="flex items-center gap-2 w-full md:w-auto">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={() => setThumbnailsVisible(!thumbnailsVisible)} className="md:hidden">
                  <PanelLeft className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Toggle Thumbnails</TooltipContent>
            </Tooltip>
            <h2 className="font-semibold text-base truncate max-w-[120px] md:max-w-xs block md:inline">KCC Development KCC 75 年發展史_revised.pdf</h2>
          </div>

          {/* Navigation row */}
          <div className="flex items-center gap-1 flex-wrap w-full md:w-auto mt-1 md:mt-0 justify-center mx-auto">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={onPrevPage} disabled={currentPageNum <= 1} className="h-8 w-8 md:h-10 md:w-10 text-base md:text-lg">
                  <ChevronLeft className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Previous Page</TooltipContent>
            </Tooltip>
            <form onSubmit={handlePageInputSubmit} className="flex items-center">
              <Input type="text" value={pageInput} onChange={handlePageInputChange} className="w-10 h-8 text-center text-sm md:w-12 md:text-base" />
              <span className="mx-2 text-xs md:text-base">/ {totalPages}</span>
            </form>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={onNextPage} disabled={currentPageNum >= totalPages} className="h-8 w-8 md:h-10 md:w-10 text-base md:text-lg">
                  <ChevronRight className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Next Page</TooltipContent>
            </Tooltip>
          </div>

          {/* Zoom row */}
          <div className="flex items-center gap-1 flex-wrap w-full md:w-auto mt-1 md:mt-0 justify-center mx-auto">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={() => handleZoom(scale - 0.2)} className="h-8 w-8 md:h-10 md:w-10 text-base md:text-lg">
                  <ZoomOut className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Zoom Out</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" className="w-16 h-8 text-xs md:w-20 md:text-base" onClick={() => fitTo("width")}>Fit Width</Button>
              </TooltipTrigger>
              <TooltipContent>Fit to Width</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" className="w-16 h-8 text-xs md:w-20 md:text-base" onClick={() => fitTo("page")}>Fit Page</Button>
              </TooltipTrigger>
              <TooltipContent>Fit to Page</TooltipContent>
            </Tooltip>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={() => handleZoom(scale + 0.2)} className="h-8 w-8 md:h-10 md:w-10 text-base md:text-lg">
                  <ZoomIn className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Zoom In</TooltipContent>
            </Tooltip>
          </div>

          {/* Download row */}
          <div className="flex items-center gap-1 flex-wrap w-full md:w-auto mt-1 md:mt-0 justify-center mx-auto mb-2">
            <div className="hidden md:flex items-center gap-1">
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={handleRotate}>
                    <RotateCw className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Rotate</TooltipContent>
              </Tooltip>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button variant="ghost" size="icon" onClick={handleFullscreen}>
                    <Expand className="h-5 w-5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Fullscreen</TooltipContent>
              </Tooltip>
            </div>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" onClick={handleDownload}>
                  <Download className="h-5 w-5" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Download</TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
          {/* Vertical sidebar for desktop (always visible) */}
          <div className="hidden md:block w-64 flex-shrink-0 h-full" style={{ maxHeight: '80vh', overflowY: 'auto' }}>
            <PdfThumbnails pdfDoc={pdfDoc} currentPage={currentPageNum} onThumbnailClick={setCurrentPageNum} />
          </div>
          <ScrollArea className="flex-1 h-full">
            <div className="flex justify-center items-center w-full h-full overflow-x-auto">
              <PdfPage pdfDoc={pdfDoc} pageNum={currentPageNum} scale={scale} rotation={rotation} />
            </div>
          </ScrollArea>
        </div>
      </div>
    </TooltipProvider>
  )
}
