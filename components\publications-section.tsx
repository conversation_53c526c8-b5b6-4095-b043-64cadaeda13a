import Link from "next/link"
import Image from "next/image"
import { ChevronRight } from "lucide-react"

export default function PublicationsSection() {
  const news = [
    "新興企業如何發展工商業務",
    "香港經濟 投資發展合作新機遇",
    "灣區經濟發展最新資訊",
    "增強競爭力 提高營運效益",
  ]

  return (
    <section className="grid md:grid-cols-2 gap-8">
      <div>
        <h2 className="text-xl font-bold mb-6 flex items-center">
          月刊（商薈）
          <ChevronRight className="h-5 w-5 text-[#1E1B4B]" />
        </h2>
        <div className="relative aspect-[3/4]">
          <Image src="/logo.jpeg" alt="Magazine Cover" fill className="object-cover rounded" />
        </div>
      </div>

      <div>
        <h2 className="text-xl font-bold mb-6 flex items-center">
          商貿通訊
          <ChevronRight className="h-5 w-5 text-[#1E1B4B]" />
        </h2>
        <div className="space-y-4">
          {news.map((title, i) => (
            <Link href="#" key={i} className="flex items-center gap-2 group">
              <ChevronRight className="h-4 w-4 text-[#1E1B4B]" />
              <span className="group-hover:text-[#1E1B4B]">{title}</span>
            </Link>
          ))}
        </div>
      </div>
    </section>
  )
}

