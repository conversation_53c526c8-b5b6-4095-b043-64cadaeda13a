import React from 'react';

interface GoogleDriveEmbedProps {
  videoId: string;
  title?: string;
  className?: string;
}

function extractVideoId(input: string): string | null {
  // If input is already a video ID (no special chars)
  if (/^[a-zA-Z0-9_-]+$/.test(input)) return input;
  
  // Try to extract from full URL
  const match = input.match(/\/d\/([a-zA-Z0-9_-]+)/);
  return match ? match[1] : null;
}

const GoogleDriveEmbed: React.FC<GoogleDriveEmbedProps> = ({ videoId, title = 'Google Drive video', className = '' }) => {
  const id = extractVideoId(videoId) || videoId;
  if (!id) return null;
  
  return (
    <div className={`aspect-w-16 aspect-h-12 w-full ${className}`.trim()}>
      <iframe
        src={`https://drive.google.com/file/d/${id}/preview`}
        title={title}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="w-full h-full rounded"
        frameBorder={0}
      />
    </div>
  );
};

export default GoogleDriveEmbed; 