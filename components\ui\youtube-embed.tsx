import React from 'react';

interface YouTubeEmbedProps {
  videoId: string;
  title?: string;
  className?: string;
}

function extractVideoId(input: string): string | null {
  // If input is already a video ID (11 chars, no special chars)
  if (/^[a-zA-Z0-9_-]{11}$/.test(input)) return input;
  // Try to extract from full URL
  const match = input.match(/(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([a-zA-Z0-9_-]{11})/);
  return match ? match[1] : null;
}

const YouTubeEmbed: React.FC<YouTubeEmbedProps> = ({ videoId, title = 'YouTube video', className = '' }) => {
  const id = extractVideoId(videoId) || videoId;
  if (!id) return null;
  return (
    <div className={`aspect-w-16 aspect-h-12 w-full ${className}`.trim()}>
      <iframe
        src={`https://www.youtube.com/embed/${id}`}
        title={title}
        allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
        allowFullScreen
        className="w-full h-full rounded"
        frameBorder={0}
      />
    </div>
  );
};

export default YouTubeEmbed; 