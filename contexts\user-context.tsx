"use client"

import { createContext, useContext, useEffect, useState } from "react"
import { fetchAPI } from "@/strapi"

type User = {
  id: number
  username: string
  email: string
}

type UserContextType = {
  user: User | null
  setUser: (user: User | null) => void
  logout: () => void
}

const UserContext = createContext<UserContextType | undefined>(undefined)

export function UserProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null)

  useEffect(() => {
    const token = localStorage.getItem("token")
    if (token) {
      fetchAPI("/users/me", {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      .then(data => {
        setUser(data)
      })
      .catch(() => {
        localStorage.removeItem("token")
        setUser(null)
      })
    }
  }, [])

  // Listen for storage events to sync logout across tabs
  useEffect(() => {
    const handleStorageChange = (e: StorageEvent) => {
      // If token is removed from localStorage (logout), clear user state
      if (e.key === "token" && e.newValue === null) {
        setUser(null)
      }
      // If token is added (login in another tab), verify and set user
      else if (e.key === "token" && e.newValue) {
        fetchAPI("/users/me", {}, {
          headers: {
            Authorization: `Bearer ${e.newValue}`
          }
        })
        .then(data => {
          setUser(data)
        })
        .catch(() => {
          localStorage.removeItem("token")
          setUser(null)
        })
      }
    }

    // Handle visibility change to check auth status when user returns to tab
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        const token = localStorage.getItem("token")
        if (!token && user) {
          // Token was removed while tab was hidden, logout user
          setUser(null)
        } else if (token && !user) {
          // Token exists but user is not set, try to authenticate
          fetchAPI("/users/me", {}, {
            headers: {
              Authorization: `Bearer ${token}`
            }
          })
          .then(data => {
            setUser(data)
          })
          .catch(() => {
            localStorage.removeItem("token")
            setUser(null)
          })
        }
      }
    }

    // Add event listeners
    window.addEventListener("storage", handleStorageChange)
    document.addEventListener("visibilitychange", handleVisibilityChange)

    // Cleanup event listeners
    return () => {
      window.removeEventListener("storage", handleStorageChange)
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [user])

  // Periodic check for authentication state consistency
  useEffect(() => {
    const checkAuthState = () => {
      const token = localStorage.getItem("token")
      if (!token && user) {
        // No token but user is set, logout
        setUser(null)
      }
    }

    // Check every 30 seconds
    const interval = setInterval(checkAuthState, 30000)

    return () => clearInterval(interval)
  }, [user])

  const logout = () => {
    const oldToken = localStorage.getItem("token")
    localStorage.removeItem("token")
    setUser(null)

    // Trigger storage event for cross-tab synchronization
    // Note: storage events don't fire in the same tab, so we manually dispatch it
    window.dispatchEvent(new StorageEvent("storage", {
      key: "token",
      newValue: null,
      oldValue: oldToken,
      storageArea: localStorage
    }))
  }

  return (
    <UserContext.Provider value={{ user, setUser, logout }}>
      {children}
    </UserContext.Provider>
  )
}

export function useUser() {
  const context = useContext(UserContext)
  if (context === undefined) {
    throw new Error("useUser must be used within a UserProvider")
  }
  return context
}