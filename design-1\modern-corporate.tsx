import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Building2, Globe2, Calendar, Phone, Mail, MapPin } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function ModernCorporate() {
  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <header className="border-b">
        <div className="container mx-auto">
          <div className="flex justify-end gap-4 py-2 text-sm border-b">
            <Link href="/about-kcc/join-us" className="text-primary hover:underline">
              Join KC<PERSON>
            </Link>
            <Link href="/member-page" className="text-primary hover:underline">
              Member Page
            </Link>
            <div className="flex gap-2 border-l pl-4">
              <Link href="?lang=zh">繁體</Link>
              <Link href="?lang=cn">简体</Link>
              <Link href="?lang=en">Eng</Link>
            </div>
          </div>
          <nav className="flex items-center justify-between py-4">
            <Link href="/" className="text-2xl font-bold">
              KCC
            </Link>
            <div className="flex gap-6">
              <Link href="/about-kcc" className="hover:text-primary">
                About KCC
              </Link>
              <Link href="/history" className="hover:text-primary">
                History
              </Link>
              <Link href="/events" className="hover:text-primary">
                Events
              </Link>
              <Link href="/business" className="hover:text-primary">
                Business
              </Link>
              <Link href="/publications" className="hover:text-primary">
                Publications
              </Link>
              <Link href="/contact-us" className="hover:text-primary">
                Contact
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-50 to-blue-100">
        <div className="container mx-auto py-24">
          <div className="grid grid-cols-2 gap-12 items-center">
            <div className="space-y-6">
              <h1 className="text-4xl font-bold leading-tight">Hong Kong KCC Elite Association</h1>
              <p className="text-lg text-muted-foreground">
                Connecting businesses, building futures in the heart of Kowloon
              </p>
              <Button size="lg">Join Our Network</Button>
            </div>
            <Image
              src="/logo.jpeg"
              alt="KCC Building"
              width={600}
              height={400}
              className="rounded-lg shadow-lg"
            />
          </div>
        </div>
      </section>

      {/* Main Content */}
      <main className="container mx-auto py-16">
        <Tabs defaultValue="news" className="space-y-8">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="news">Latest News</TabsTrigger>
            <TabsTrigger value="events">Upcoming Events</TabsTrigger>
          </TabsList>
          <TabsContent value="news" className="space-y-4">
            <Card>
              <CardContent className="p-6">
                <time className="text-sm text-muted-foreground">2021-01-01</time>
                <h3 className="text-xl font-semibold mt-2">2021年龍總活動</h3>
                <p className="mt-2 text-muted-foreground">Event description and details...</p>
              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="events">{/* Event content */}</TabsContent>
        </Tabs>

        {/* Business Section */}
        <section className="mt-16">
          <h2 className="text-2xl font-bold mb-8">Business Opportunities</h2>
          <div className="grid grid-cols-3 gap-6">
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Building2 className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold">Local Business</h3>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Globe2 className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold">International Business</h3>
              </CardContent>
            </Card>
            <Card className="hover:shadow-lg transition-shadow">
              <CardContent className="p-6 text-center">
                <Calendar className="w-12 h-12 mx-auto mb-4 text-primary" />
                <h3 className="font-semibold">Exhibition</h3>
              </CardContent>
            </Card>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="bg-gray-50 border-t">
        <div className="container mx-auto py-12">
          <div className="grid grid-cols-4 gap-8">
            <div className="space-y-4">
              <h3 className="font-semibold">Contact Us</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  <span>2760 0393</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>3/F KCC Building, 2 Liberty Avenue, Kowloon</span>
                </div>
              </div>
            </div>
            <div>
              <h3 className="font-semibold mb-4">Quick Links</h3>
              <div className="space-y-2 text-sm">
                <Link href="/about-kcc" className="block hover:text-primary">
                  About KCC
                </Link>
                <Link href="/history" className="block hover:text-primary">
                  History
                </Link>
                <Link href="/events" className="block hover:text-primary">
                  Events
                </Link>
              </div>
            </div>
            {/* Add more footer columns */}
          </div>
          <div className="border-t mt-8 pt-8 text-center text-sm text-muted-foreground">
            <p>Copyright © 2024 Kowloon Chamber of Commerce</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

