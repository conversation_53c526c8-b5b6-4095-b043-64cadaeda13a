import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import { Building2, Phone, Mail, MapPin } from "lucide-react"
import Link from "next/link"

export default function TraditionalElegant() {
  return (
    <div className="min-h-screen bg-[#FDF8F3]">
      {/* Header */}
      <header className="bg-white border-b border-[#E8D5C4]">
        <div className="container mx-auto">
          <div className="flex justify-end gap-4 py-2 text-sm border-b border-[#E8D5C4]">
            <Link href="/about-kcc/join-us" className="text-[#9E2A2B] hover:underline">
              Join <PERSON>
            </Link>
            <Link href="/member-page" className="text-[#9E2A2B] hover:underline">
              Member Page
            </Link>
            <div className="flex gap-2 border-l border-[#E8D5C4] pl-4">
              <Link href="?lang=zh">繁體</Link>
              <Link href="?lang=cn">简体</Link>
              <Link href="?lang=en">Eng</Link>
            </div>
          </div>
          <nav className="flex items-center justify-between py-6">
            <Link href="/" className="flex items-center gap-3">
              <div className="w-12 h-12 bg-[#9E2A2B] rounded-full flex items-center justify-center">
                <span className="text-white font-bold">KCC</span>
              </div>
              <span className="text-xl font-medium">Kowloon Chamber</span>
            </Link>
            <div className="flex gap-8">
              <Link href="/about-kcc" className="hover:text-[#9E2A2B]">
                About KCC
              </Link>
              <Link href="/history" className="hover:text-[#9E2A2B]">
                History
              </Link>
              <Link href="/events" className="hover:text-[#9E2A2B]">
                Events
              </Link>
              <Link href="/business" className="hover:text-[#9E2A2B]">
                Business
              </Link>
              <Link href="/publications" className="hover:text-[#9E2A2B]">
                Publications
              </Link>
              <Link href="/contact-us" className="hover:text-[#9E2A2B]">
                Contact
              </Link>
            </div>
          </nav>
        </div>
      </header>

      {/* Hero Section */}
      <section className="relative bg-[#9E2A2B] text-white">
        <div className="absolute inset-0 bg-[url('/logo.jpeg')] opacity-20"></div>
        <div className="container mx-auto py-24 relative">
          <div className="max-w-2xl space-y-6">
            <h1 className="text-4xl font-bold leading-tight">Hong Kong KCC Elite Association</h1>
            <p className="text-lg opacity-90">Preserving tradition, embracing innovation in the heart of Kowloon</p>
            <Button variant="secondary" size="lg">
              Join Our Chamber
            </Button>
          </div>
        </div>
      </section>

      {/* News & Events */}
      <section className="container mx-auto py-16">
        <div className="grid md:grid-cols-2 gap-12">
          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Latest News</h2>
              <Link href="/news" className="text-[#9E2A2B] hover:underline">
                View All
              </Link>
            </div>
            <Card className="bg-white border-[#E8D5C4]">
              <CardContent className="p-6">
                <time className="text-sm text-muted-foreground">2021-01-01</time>
                <h3 className="text-xl font-semibold mt-2">2021年龍總活動</h3>
                <p className="mt-2 text-muted-foreground">Event details and description...</p>
              </CardContent>
            </Card>
          </div>

          <div>
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-2xl font-bold">Upcoming Events</h2>
              <Link href="/events" className="text-[#9E2A2B] hover:underline">
                View All
              </Link>
            </div>
            {/* Event content */}
          </div>
        </div>
      </section>

      {/* Business Section */}
      <section className="bg-white py-16 border-y border-[#E8D5C4]">
        <div className="container mx-auto">
          <h2 className="text-2xl font-bold text-center mb-12">Business Opportunities</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="border-[#E8D5C4] hover:shadow-lg transition-shadow">
              <CardContent className="p-8 text-center">
                <Building2 className="w-12 h-12 mx-auto mb-4 text-[#9E2A2B]" />
                <h3 className="font-semibold text-lg mb-2">Local Business</h3>
                <p className="text-muted-foreground">Connect with local enterprises</p>
              </CardContent>
            </Card>
            {/* Add more business cards */}
          </div>
        </div>
      </section>

      {/* Links Section */}
      <section className="container mx-auto py-16">
        <div className="grid md:grid-cols-2 gap-12">
          <div>
            <h2 className="text-2xl font-bold mb-6">Our Functions</h2>
            <div className="grid grid-cols-2 gap-4">
              <Link href="/chamber-activities" className="group">
                <Card className="border-[#E8D5C4] hover:border-[#9E2A2B] transition-colors">
                  <CardContent className="p-4">
                    <span className="group-hover:text-[#9E2A2B]">Chamber Activities</span>
                  </CardContent>
                </Card>
              </Link>
              {/* Add more function cards */}
            </div>
          </div>

          <div>
            <h2 className="text-2xl font-bold mb-6">Partner Links</h2>
            <div className="grid grid-cols-2 gap-4">
              <Link href="http://www.locpg.hk/" target="_blank" className="group">
                <Card className="border-[#E8D5C4] hover:border-[#9E2A2B] transition-colors">
                  <CardContent className="p-4">
                    <span className="group-hover:text-[#9E2A2B]">Liaison Office</span>
                  </CardContent>
                </Card>
              </Link>
              {/* Add more partner links */}
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-[#2C1810] text-white">
        <div className="container mx-auto py-12">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <h3 className="font-semibold mb-4">Contact Us</h3>
              <div className="space-y-2 text-zinc-400">
                <div className="flex items-center gap-2">
                  <Mail className="w-4 h-4" />
                  <span><EMAIL></span>
                </div>
                <div className="flex items-center gap-2">
                  <Phone className="w-4 h-4" />
                  <span>2760 0393</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="w-4 h-4" />
                  <span>3/F KCC Building, 2 Liberty Avenue, Kowloon</span>
                </div>
              </div>
            </div>
            {/* Add more footer columns */}
          </div>
          <Separator className="my-8 bg-zinc-800" />
          <div className="text-center text-zinc-400">
            <p>Copyright © 2024 Kowloon Chamber of Commerce</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

