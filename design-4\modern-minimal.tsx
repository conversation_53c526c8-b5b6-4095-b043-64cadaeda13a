"use client"

import { useState } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON>rollArea } from "@/components/ui/scroll-area"
import { Menu, X, ChevronRight, Building2, Globe2, Phone, Mail, MapPin, Sun, Moon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

export default function ModernMinimal() {
  const [isSidebarOpen, setSidebarOpen] = useState(false)
  const [isDarkMode, setDarkMode] = useState(false)

  const toggleDarkMode = () => {
    setDarkMode(!isDarkMode)
    document.documentElement.classList.toggle("dark")
  }

  return (
    <div className={`min-h-screen bg-background ${isDarkMode ? "dark" : ""}`}>
      {/* Mobile Header */}
      <header className="lg:hidden border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex items-center justify-between h-16">
          <Button variant="ghost" size="icon" onClick={() => setSidebarOpen(true)}>
            <Menu className="h-6 w-6" />
          </Button>
          <Link href="/" className="font-semibold">
            KCC
          </Link>
          <div className="flex gap-2 text-sm">
            <Link href="?lang=zh">繁</Link>
            <Link href="?lang=cn">简</Link>
            <Link href="?lang=en">En</Link>
          </div>
        </div>
      </header>

      {/* Sidebar Navigation */}
      <aside
        className={`
        fixed top-0 left-0 z-40 h-full w-64 bg-background border-r transition-transform duration-200 ease-in-out
        ${isSidebarOpen ? "translate-x-0" : "-translate-x-full"}
        lg:translate-x-0
      `}
      >
        <ScrollArea className="h-full">
          <div className="p-6">
            <div className="flex items-center justify-between mb-8">
              <Link href="/" className="font-bold text-xl">
                KCC
              </Link>
              <Button variant="ghost" size="icon" className="lg:hidden" onClick={() => setSidebarOpen(false)}>
                <X className="h-5 w-5" />
              </Button>
            </div>

            <nav className="space-y-6">
              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Main</div>
                <div className="space-y-1">
                  <Link href="/about-kcc" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    About KCC
                  </Link>
                  <Link href="/history" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    History
                  </Link>
                  <Link href="/events" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    Events
                  </Link>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Business</div>
                <div className="space-y-1">
                  <Link href="/business" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    Opportunities
                  </Link>
                  <Link href="/publications" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    Publications
                  </Link>
                  <Link href="/kcc-building" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    Apartments
                  </Link>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium text-muted-foreground">Member</div>
                <div className="space-y-1">
                  <Link
                    href="/about-kcc/join-us"
                    className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm"
                  >
                    Join KCC
                  </Link>
                  <Link href="/member-page" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    Member Page
                  </Link>
                  <Link href="/contact-us" className="flex items-center h-9 px-3 rounded-md hover:bg-accent text-sm">
                    Contact Us
                  </Link>
                </div>
              </div>
            </nav>

            <div className="mt-8 pt-8 border-t">
              <Button variant="outline" className="w-full" onClick={toggleDarkMode}>
                {isDarkMode ? <Sun className="h-4 w-4 mr-2" /> : <Moon className="h-4 w-4 mr-2" />}
                {isDarkMode ? "Light Mode" : "Dark Mode"}
              </Button>
            </div>
          </div>
        </ScrollArea>
      </aside>

      {/* Main Content */}
      <div className="lg:pl-64">
        {/* Hero Section */}
        <section className="relative h-[80vh] overflow-hidden">
          <Image src="/logo.jpeg" alt="KCC Building" fill className="object-cover" priority />
          <div className="absolute inset-0 bg-gradient-to-r from-background/90 to-background/30">
            <div className="container h-full flex items-center">
              <div className="max-w-2xl space-y-6">
                <h1 className="text-4xl font-bold tracking-tight sm:text-6xl">Hong Kong KCC Elite Association</h1>
                <p className="text-lg text-muted-foreground">
                  Building bridges between businesses in the heart of Kowloon
                </p>
                <div className="flex gap-4">
                  <Button size="lg">Join Our Network</Button>
                  <Button variant="outline" size="lg">
                    Learn More
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* News & Events Split Section */}
        <section className="grid lg:grid-cols-2">
          <div className="p-8 lg:p-12 border-b lg:border-r">
            <div className="max-w-xl">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-semibold">Latest News</h2>
                <Link href="/news" className="text-primary hover:underline">
                  View All
                </Link>
              </div>
              <div className="space-y-6">
                <article className="group">
                  <time className="text-sm text-muted-foreground">2021-01-01</time>
                  <h3 className="text-lg font-medium mt-1 group-hover:text-primary">2021年龍總活動</h3>
                  <p className="mt-2 text-muted-foreground">Event details and description...</p>
                  <Button variant="link" className="px-0 mt-2">
                    Read More <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </article>
                {/* Add more news items */}
              </div>
            </div>
          </div>

          <div className="p-8 lg:p-12 border-b">
            <div className="max-w-xl">
              <div className="flex items-center justify-between mb-8">
                <h2 className="text-2xl font-semibold">Upcoming Events</h2>
                <Link href="/events" className="text-primary hover:underline">
                  View All
                </Link>
              </div>
              {/* Event content */}
            </div>
          </div>
        </section>

        {/* Business Opportunities */}
        <section className="py-16 lg:py-24">
          <div className="container">
            <h2 className="text-2xl font-semibold mb-12">Business Opportunities</h2>
            <div className="grid sm:grid-cols-2 lg:grid-cols-4 gap-6">
              <Link href="/business/local" className="group">
                <Card className="h-full transition-all hover:shadow-lg hover:-translate-y-1">
                  <CardContent className="p-6">
                    <Building2 className="h-8 w-8 mb-4 text-primary" />
                    <h3 className="font-medium mb-2 group-hover:text-primary">Local Business</h3>
                    <p className="text-sm text-muted-foreground">
                      Connect with local enterprises and expand your network
                    </p>
                  </CardContent>
                </Card>
              </Link>
              <Link href="/business/international" className="group">
                <Card className="h-full transition-all hover:shadow-lg hover:-translate-y-1">
                  <CardContent className="p-6">
                    <Globe2 className="h-8 w-8 mb-4 text-primary" />
                    <h3 className="font-medium mb-2 group-hover:text-primary">International Trade</h3>
                    <p className="text-sm text-muted-foreground">Explore global opportunities and partnerships</p>
                  </CardContent>
                </Card>
              </Link>
              {/* Add more business cards */}
            </div>
          </div>
        </section>

        {/* Member Links Grid */}
        <section className="bg-muted/50 py-16 lg:py-24">
          <div className="container">
            <h2 className="text-2xl font-semibold mb-12">Member Organizations</h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-6 gap-4">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="aspect-square">
                  <CardContent className="p-4 h-full flex items-center justify-center">
                    <Image
                      src="/logo.jpeg"
                      alt={`Member ${i + 1}`}
                      width={100}
                      height={100}
                      className="opacity-75 hover:opacity-100 transition-opacity"
                    />
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        </section>

        {/* Contact Section */}
        <section className="border-t">
          <div className="container py-16 lg:py-24">
            <div className="grid lg:grid-cols-2 gap-12">
              <div>
                <h2 className="text-2xl font-semibold mb-8">Get in Touch</h2>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <Mail className="h-5 w-5 text-muted-foreground" />
                    <span><EMAIL></span>
                  </div>
                  <div className="flex items-center gap-3">
                    <Phone className="h-5 w-5 text-muted-foreground" />
                    <span>Tel: 2760 0393 | Fax: 2761 0166</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <MapPin className="h-5 w-5 text-muted-foreground" />
                    <span>3/F KCC Building, 2 Liberty Avenue, Kowloon</span>
                  </div>
                </div>
              </div>
              <div className="lg:text-right">
                <h2 className="text-2xl font-semibold mb-8">Join Our Network</h2>
                <p className="text-muted-foreground mb-6">
                  Become a member of Kowloon Chamber of Commerce and unlock exclusive benefits
                </p>
                <Button size="lg">Apply for Membership</Button>
              </div>
            </div>
          </div>
        </section>

        {/* Footer */}
        <footer className="border-t">
          <div className="container py-8">
            <div className="flex flex-col sm:flex-row justify-between items-center gap-4">
              <p className="text-sm text-muted-foreground">Copyright © 2024 Kowloon Chamber of Commerce</p>
              <nav className="flex gap-6">
                <Link href="/privacy" className="text-sm text-muted-foreground hover:text-foreground">
                  Privacy Policy
                </Link>
                <Link href="/terms" className="text-sm text-muted-foreground hover:text-foreground">
                  Terms of Service
                </Link>
              </nav>
            </div>
          </div>
        </footer>
      </div>
    </div>
  )
}

