import { useEffect, useState } from 'react'

// Utility functions to handle hydration issues

export function suppressHydrationWarnings() {
  if (typeof window !== 'undefined') {
    // Suppress console warnings for hydration mismatches
    const originalError = console.error
    console.error = (...args: any[]) => {
      const message = args[0]
      if (
        typeof message === 'string' &&
        (message.includes('Hydration failed') ||
         message.includes('Text content does not match server-rendered HTML') ||
         message.includes('Warning: Text content did not match'))
      ) {
        // Suppress hydration warnings caused by browser extensions
        console.warn('Hydration warning suppressed (likely caused by browser extension):', message)
        return
      }
      originalError.apply(console, args)
    }

    // Clean up Grammarly extension attributes that cause hydration issues
    const cleanupGrammarlyAttributes = () => {
      const elements = document.querySelectorAll('[data-new-gr-c-s-check-loaded], [data-gr-ext-installed]')
      elements.forEach(element => {
        element.removeAttribute('data-new-gr-c-s-check-loaded')
        element.removeAttribute('data-gr-ext-installed')
      })
    }

    // Run cleanup after DOM is ready
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', cleanupGrammarlyAttributes)
    } else {
      cleanupGrammarlyAttributes()
    }

    // Also run cleanup periodically
    setInterval(cleanupGrammarlyAttributes, 1000)
  }
}

export function isClient() {
  return typeof window !== 'undefined'
}

export function isServer() {
  return typeof window === 'undefined'
}

// Hook to handle hydration safely
export function useHydrationSafe() {
  const [isHydrated, setIsHydrated] = useState(false)

  useEffect(() => {
    setIsHydrated(true)
  }, [])

  return isHydrated
} 