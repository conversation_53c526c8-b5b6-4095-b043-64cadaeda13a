// Optimized PDF Loading Strategy
export class OptimizedPdfLoader {
  private static cache = new Map<string, { url: string; timestamp: number }>()
  private static readonly CACHE_DURATION = 30 * 60 * 1000 // 30 minutes
  
  static async loadPdfUrl(
    fetchFunction: () => Promise<string>,
    cacheKey: string,
    fallbackUrl: string
  ): Promise<string> {
    
    // 1. Check memory cache first (fastest)
    const cached = this.cache.get(cacheKey)
    if (cached && Date.now() - cached.timestamp < this.CACHE_DURATION) {
      return cached.url
    }
    
    // 2. Check localStorage cache
    const localCached = this.getFromLocalStorage(cacheKey)
    if (localCached) {
      this.cache.set(cacheKey, { url: localCached, timestamp: Date.now() })
      return localCached
    }
    
    // 3. Fetch from API with timeout
    try {
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 3000) // 3 second timeout
      
      const url = await Promise.race([
        fetchFunction(),
        new Promise<string>((_, reject) => 
          setTimeout(() => reject(new Error('Timeout')), 3000)
        )
      ])
      
      clearTimeout(timeoutId)
      
      if (url && url.trim()) {
        // Cache in both memory and localStorage
        this.cache.set(cacheKey, { url, timestamp: Date.now() })
        this.setToLocalStorage(cacheKey, url)
        return url
      }
    } catch (error) {
      console.warn('Failed to fetch PDF URL from API:', error)
    }
    
    // 4. Return fallback
    return fallbackUrl
  }
  
  private static getFromLocalStorage(key: string): string | null {
    if (typeof window === 'undefined') return null
    
    try {
      const item = localStorage.getItem(`pdf_${key}`)
      if (!item) return null
      
      const { url, timestamp } = JSON.parse(item)
      if (Date.now() - timestamp < this.CACHE_DURATION) {
        return url
      }
      
      localStorage.removeItem(`pdf_${key}`)
      return null
    } catch {
      return null
    }
  }
  
  private static setToLocalStorage(key: string, url: string): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem(`pdf_${key}`, JSON.stringify({
        url,
        timestamp: Date.now()
      }))
    } catch (error) {
      console.warn('Failed to cache PDF URL:', error)
    }
  }
  
  // Preload PDFs in background
  static preloadPdfs(urls: string[]): void {
    if ('serviceWorker' in navigator && 'caches' in window) {
      caches.open('pdf-preload').then(cache => {
        urls.forEach(url => {
          cache.add(url).catch(() => {}) // Silent fail
        })
      })
    }
  }
  
  // Clear all caches
  static clearCache(): void {
    this.cache.clear()
    if (typeof window !== 'undefined') {
      Object.keys(localStorage)
        .filter(key => key.startsWith('pdf_'))
        .forEach(key => localStorage.removeItem(key))
    }
  }
}
