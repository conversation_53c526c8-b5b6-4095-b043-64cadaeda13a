// Session-only PDF URL caching utility (best practice)
const CACHE_KEY = 'kcc_pdf_urls_session'
const CACHE_DURATION = 10 * 60 * 1000 // 10 minutes max per session

interface CacheEntry {
  url: string
  timestamp: number
}

interface PdfUrlCache {
  [key: string]: CacheEntry
}

export function getCachedPdfUrl(key: string): string | null {
  if (typeof window === 'undefined') return null
  
  try {
    const cached = localStorage.getItem(CACHE_KEY)
    if (!cached) return null

    const cache: PdfUrlCache = JSON.parse(cached)
    const entry = cache[key]
    
    if (entry && Date.now() - entry.timestamp < CACHE_DURATION) {
      return entry.url
    }
    
    // Remove expired entry
    if (entry) {
      delete cache[key]
      localStorage.setItem(CACHE_KEY, JSON.stringify(cache))
    }
    
    return null
  } catch {
    return null
  }
}

export function setCachedPdfUrl(key: string, url: string): void {
  if (typeof window === 'undefined') return
  
  try {
    const cached = localStorage.getItem(CACHE_KEY)
    const cache: PdfUrlCache = cached ? JSON.parse(cached) : {}
    
    cache[key] = {
      url,
      timestamp: Date.now()
    }
    
    localStorage.setItem(CACHE_KEY, JSON.stringify(cache))
  } catch (error) {
    console.warn('Failed to cache PDF URL:', error)
  }
}

export function clearPdfCache(): void {
  if (typeof window === 'undefined') return

  try {
    localStorage.removeItem(CACHE_KEY)
    // Also clear any old cache keys
    Object.keys(localStorage)
      .filter(key => key.startsWith('kcc_pdf_urls'))
      .forEach(key => localStorage.removeItem(key))
  } catch (error) {
    console.warn('Failed to clear PDF cache:', error)
  }
}

// Auto-cleanup on page unload (best practice)
if (typeof window !== 'undefined') {
  window.addEventListener('beforeunload', () => {
    clearPdfCache()
  })

  // Also cleanup on visibility change (when user switches tabs/apps)
  document.addEventListener('visibilitychange', () => {
    if (document.visibilityState === 'hidden') {
      // Clear cache when user leaves the page
      setTimeout(clearPdfCache, 30000) // 30 seconds delay
    }
  })
}
