{"name": "my-v0-project", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@edge-runtime/vm": "latest", "@emotion/is-prop-valid": "latest", "@hookform/resolvers": "^3.9.1", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.2", "@radix-ui/react-context-menu": "^2.2.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-hover-card": "^1.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-menubar": "^1.1.4", "@radix-ui/react-navigation-menu": "^1.2.3", "@radix-ui/react-popover": "^1.1.4", "@radix-ui/react-progress": "^1.1.1", "@radix-ui/react-radio-group": "^1.2.2", "@radix-ui/react-scroll-area": "^1.2.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.2.2", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "latest", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.1", "@radix-ui/react-toggle-group": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.6", "@strapi/blocks-react-renderer": "^1.0.2", "@testing-library/dom": "latest", "@testing-library/jest-dom": "latest", "@testing-library/react": "latest", "@types/debug": "latest", "@types/node": "latest", "@types/qs": "^6.9.18", "@types/react": "^18", "@types/react-dom": "^18", "@vitest/browser": "latest", "@vitest/ui": "latest", "autoprefixer": "^10.4.20", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.4", "date-fns": "^3.0.0", "embla-carousel-react": "8.5.1", "framer-motion": "latest", "happy-dom": "latest", "input-otp": "1.4.1", "jsdom": "latest", "lucide-react": "^0.454.0", "next": "15.1.0", "next-themes": "^0.4.6", "qs": "^6.14.0", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.54.1", "react-resizable-panels": "^2.1.7", "recharts": "2.15.0", "sonner": "^1.7.4", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "vaul": "^0.9.6", "vitest": "latest", "zod": "^3.24.1"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@types/node": "^22", "@types/react": "^18", "@types/react-dom": "^18", "husky": "^9.1.7", "postcss": "^8", "tailwindcss": "^3.4.17", "typescript": "^5"}}