import qs from "qs"

const STRAPI_URL = process.env.NEXT_PUBLIC_STRAPI_URL || "https://strapibackendproject-3x6s.onrender.com/api"
const STRAPI_TOKEN = process.env.NEXT_PUBLIC_STRAPI_TOKEN

export async function fetchAPI(path: string, urlParamsObject = {}, options = {}) {
  try {
    // Determine if this is an auth request
    const isAuthRequest = path.startsWith('auth/');

    // Merge default and user options
    const mergedOptions = {
      next: { revalidate: 60 },
      headers: {
        "Content-Type": "application/json",
        // Only add Authorization header for non-auth requests
        ...(isAuthRequest ? {} : { Authorization: `Bearer ${STRAPI_TOKEN}` }),
      },
      ...options,
    }

    // Build request URL
    const queryString = qs.stringify(urlParamsObject)
    // Remove any leading slash from path
    const cleanPath = path.startsWith('/') ? path.substring(1) : path
    const requestUrl = `${STRAPI_URL}/${cleanPath}${queryString ? `?${queryString}` : ""}`
    


    // Trigger API call
    const response = await fetch(requestUrl, mergedOptions)
    if (!response.ok) {
      // Try to get error details from response
      let errorData;
      try {
        errorData = await response.json();
      } catch (e) {
        errorData = { message: response.statusText };
      }

      const error = new Error(`HTTP error! status: ${response.status}`);
      (error as any).response = {
        status: response.status,
        statusText: response.statusText,
        data: errorData
      };

      // For 400/401, return error data instead of throwing
      if ([400, 401].includes(response.status)) {
        return { error: errorData };
      }
      throw error;
    }
    const data = await response.json()
    
    return data

  } catch (error) {
    console.error('API Error:', error)
    throw error
  }
}

export async function fetchRentals(params?: any) {
  try {
   
    const response = await fetchAPI('rentals', params)
   
    return response
  } catch (error) {
    // console.error('Error fetching rentals:', error)
    throw error
  }
} 